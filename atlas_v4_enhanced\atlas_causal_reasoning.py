"""
A.T.L.A.S. Causal Reasoning Engine
Advanced causal inference for market analysis and trading decisions
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Core imports
from models import EngineStatus

# Causal inference imports (with graceful fallbacks)
try:
    import networkx as nx
    from dowhy import CausalModel
    from econml.dml import LinearDML
    from causalml.inference.meta import LRSRegressor
    CAUSAL_LIBS_AVAILABLE = True
except ImportError:
    CAUSAL_LIBS_AVAILABLE = False
    nx = None

# Grok integration (with graceful fallback)
try:
    from atlas_grok_integration import AtlasGrokIntegrationEngine, GrokTaskType, GrokCapability
    GROK_INTEGRATION_AVAILABLE = True
except ImportError:
    GROK_INTEGRATION_AVAILABLE = False

logger = logging.getLogger(__name__)

# ============================================================================
# CAUSAL REASONING MODELS
# ============================================================================

class CausalRelationType(Enum):
    """Types of causal relationships in markets"""
    DIRECT = "direct"
    INDIRECT = "indirect"
    CONFOUNDED = "confounded"
    SPURIOUS = "spurious"
    BIDIRECTIONAL = "bidirectional"

@dataclass
class CausalRelationship:
    """Represents a causal relationship between market variables"""
    cause: str
    effect: str
    relationship_type: CausalRelationType
    strength: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    time_lag: int  # in time periods
    confounders: List[str]
    evidence: Dict[str, Any]
    timestamp: datetime

@dataclass
class CausalScenario:
    """Represents a what-if scenario analysis"""
    scenario_id: str
    intervention: Dict[str, float]  # variable -> new value
    predicted_effects: Dict[str, float]  # variable -> predicted change
    confidence: float
    time_horizon: int
    assumptions: List[str]
    timestamp: datetime

# ============================================================================
# CAUSAL REASONING ENGINE
# ============================================================================

class AtlasCausalReasoningEngine:
    """Advanced causal reasoning engine for market analysis"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.causal_libs_available = CAUSAL_LIBS_AVAILABLE
        self.grok_integration_available = GROK_INTEGRATION_AVAILABLE

        # Causal graph and relationships
        self.causal_graph = None
        self.relationships = {}
        self.confounders = {}

        # Market variables for causal analysis
        self.market_variables = [
            'price', 'volume', 'volatility', 'sentiment', 'momentum',
            'interest_rates', 'vix', 'sector_performance', 'earnings',
            'news_sentiment', 'options_flow', 'institutional_flow'
        ]

        # Scenario cache
        self.scenario_cache = {}
        self.max_cache_size = 1000

        # Grok integration
        self.grok_engine = None
        self.grok_enhanced_results = {}

        logger.info(f"[CAUSAL] Causal Reasoning Engine initialized - libs: {self.causal_libs_available}, grok: {self.grok_integration_available}")

    async def initialize(self):
        """Initialize the causal reasoning engine"""
        try:
            self.status = EngineStatus.INITIALIZING

            if self.causal_libs_available:
                # Initialize causal graph
                await self._initialize_causal_graph()

                # Build market relationships
                await self._build_market_relationships()

                logger.info("[OK] Causal Reasoning Engine fully initialized")
            else:
                logger.warning("[FALLBACK] Causal libraries not available - using simplified reasoning")

            # Initialize Grok integration
            if self.grok_integration_available:
                try:
                    self.grok_engine = AtlasGrokIntegrationEngine()
                    grok_success = await self.grok_engine.initialize()
                    if grok_success:
                        logger.info("[OK] Grok integration initialized for causal reasoning")
                    else:
                        logger.warning("[FALLBACK] Grok API not available - enhanced reasoning disabled")
                except Exception as e:
                    logger.error(f"Grok integration initialization failed: {e}")
                    self.grok_engine = None
            else:
                logger.warning("[FALLBACK] Grok integration not available")

            self.status = EngineStatus.ACTIVE

        except Exception as e:
            logger.error(f"Causal reasoning engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_causal_graph(self):
        """Initialize the causal graph structure"""
        if not self.causal_libs_available:
            return
        
        try:
            # Create directed graph for causal relationships
            self.causal_graph = nx.DiGraph()
            
            # Add market variables as nodes
            for variable in self.market_variables:
                self.causal_graph.add_node(variable)
            
            # Define known causal relationships in financial markets
            causal_edges = [
                ('interest_rates', 'price', {'strength': 0.7, 'lag': 1}),
                ('earnings', 'price', {'strength': 0.8, 'lag': 0}),
                ('sentiment', 'price', {'strength': 0.6, 'lag': 0}),
                ('volume', 'volatility', {'strength': 0.5, 'lag': 0}),
                ('vix', 'volatility', {'strength': 0.9, 'lag': 0}),
                ('news_sentiment', 'sentiment', {'strength': 0.7, 'lag': 0}),
                ('institutional_flow', 'price', {'strength': 0.6, 'lag': 1}),
                ('options_flow', 'volatility', {'strength': 0.5, 'lag': 0}),
                ('momentum', 'price', {'strength': 0.4, 'lag': 1}),
                ('sector_performance', 'price', {'strength': 0.5, 'lag': 0})
            ]
            
            # Add edges to graph
            for cause, effect, attrs in causal_edges:
                self.causal_graph.add_edge(cause, effect, **attrs)
            
            logger.info(f"[GRAPH] Causal graph initialized with {len(self.market_variables)} nodes")
            
        except Exception as e:
            logger.error(f"Causal graph initialization failed: {e}")
            raise

    async def _build_market_relationships(self):
        """Build detailed causal relationships from graph"""
        if not self.causal_libs_available or not self.causal_graph:
            return
        
        try:
            for edge in self.causal_graph.edges(data=True):
                cause, effect, attrs = edge
                
                # Create causal relationship object
                relationship = CausalRelationship(
                    cause=cause,
                    effect=effect,
                    relationship_type=CausalRelationType.DIRECT,
                    strength=attrs.get('strength', 0.5),
                    confidence=0.8,  # Default confidence
                    time_lag=attrs.get('lag', 0),
                    confounders=[],  # Will be populated during analysis
                    evidence={},
                    timestamp=datetime.now()
                )
                
                # Store relationship
                key = f"{cause}->{effect}"
                self.relationships[key] = relationship
            
            logger.info(f"[RELATIONSHIPS] Built {len(self.relationships)} causal relationships")
            
        except Exception as e:
            logger.error(f"Market relationships building failed: {e}")
            raise

    async def analyze_causal_impact(self, symbol: str, intervention: Dict[str, float], 
                                  time_horizon: int = 5) -> Dict[str, Any]:
        """Analyze causal impact of interventions on market variables"""
        try:
            if not self.causal_libs_available:
                return await self._fallback_causal_analysis(symbol, intervention, time_horizon)
            
            # Create scenario ID
            scenario_id = f"{symbol}_{hash(str(intervention))}_{time_horizon}"
            
            # Check cache
            if scenario_id in self.scenario_cache:
                cached = self.scenario_cache[scenario_id]
                if (datetime.now() - cached.timestamp).seconds < 300:  # 5 min cache
                    return self._format_causal_response(cached)
            
            # Perform causal analysis
            predicted_effects = {}
            confidence_scores = {}
            
            for variable, new_value in intervention.items():
                if variable in self.market_variables:
                    # Find all variables affected by this intervention
                    affected_vars = await self._find_causal_descendants(variable)
                    
                    for affected_var in affected_vars:
                        # Calculate predicted effect
                        effect = await self._calculate_causal_effect(
                            variable, affected_var, new_value, symbol
                        )
                        
                        if effect is not None:
                            predicted_effects[affected_var] = effect['magnitude']
                            confidence_scores[affected_var] = effect['confidence']
            
            # Create scenario
            scenario = CausalScenario(
                scenario_id=scenario_id,
                intervention=intervention,
                predicted_effects=predicted_effects,
                confidence=np.mean(list(confidence_scores.values())) if confidence_scores else 0.5,
                time_horizon=time_horizon,
                assumptions=[
                    "Market structure remains stable",
                    "No external shocks during analysis period",
                    "Historical relationships hold"
                ],
                timestamp=datetime.now()
            )
            
            # Cache scenario
            self._cache_scenario(scenario)

            # Enhance with Grok reasoning if available
            base_response = self._format_causal_response(scenario)

            if self.grok_engine:
                try:
                    enhanced_result = await self.grok_engine.enhance_causal_reasoning(
                        base_response, symbol, intervention
                    )

                    # Store enhanced result for future reference
                    self.grok_enhanced_results[scenario_id] = enhanced_result

                    # Combine original and enhanced results
                    enhanced_response = base_response.copy()
                    enhanced_response.update({
                        'grok_enhanced': True,
                        'enhanced_confidence': enhanced_result.combined_confidence,
                        'grok_insights': enhanced_result.grok_enhancement.content if enhanced_result.grok_enhancement.success else None,
                        'reasoning_chain': enhanced_result.reasoning_chain,
                        'improvement_metrics': enhanced_result.improvement_metrics,
                        'original_confidence': base_response.get('confidence', 0.5)
                    })

                    logger.info(f"[GROK] Enhanced causal analysis for {symbol} - confidence improved from {base_response.get('confidence', 0.5):.2f} to {enhanced_result.combined_confidence:.2f}")
                    return enhanced_response

                except Exception as e:
                    logger.error(f"Grok enhancement failed for causal analysis: {e}")
                    # Return original result if enhancement fails
                    base_response['grok_enhanced'] = False
                    base_response['grok_error'] = str(e)

            return base_response
            
        except Exception as e:
            logger.error(f"Causal impact analysis failed for {symbol}: {e}")
            return {'error': str(e), 'fallback': True}

    async def _find_causal_descendants(self, variable: str) -> List[str]:
        """Find all variables causally affected by the given variable"""
        if not self.causal_graph:
            return []
        
        try:
            # Use NetworkX to find all descendants
            descendants = list(nx.descendants(self.causal_graph, variable))
            return descendants
            
        except Exception as e:
            logger.error(f"Finding causal descendants failed: {e}")
            return []

    async def _calculate_causal_effect(self, cause: str, effect: str, 
                                     intervention_value: float, symbol: str) -> Optional[Dict[str, Any]]:
        """Calculate the causal effect of intervention on target variable"""
        try:
            # Get relationship
            key = f"{cause}->{effect}"
            if key not in self.relationships:
                return None
            
            relationship = self.relationships[key]
            
            # Simple linear causal effect calculation
            # In production, this would use more sophisticated causal inference
            base_effect = intervention_value * relationship.strength
            
            # Apply time lag
            if relationship.time_lag > 0:
                base_effect *= 0.9 ** relationship.time_lag  # Decay over time
            
            # Add some market-specific adjustments
            market_adjustment = await self._get_market_adjustment(symbol, cause, effect)
            adjusted_effect = base_effect * market_adjustment
            
            return {
                'magnitude': adjusted_effect,
                'confidence': relationship.confidence,
                'time_lag': relationship.time_lag,
                'relationship_strength': relationship.strength
            }
            
        except Exception as e:
            logger.error(f"Causal effect calculation failed: {e}")
            return None

    async def _get_market_adjustment(self, symbol: str, cause: str, effect: str) -> float:
        """Get market-specific adjustment factor for causal effects"""
        try:
            # Simplified market adjustment based on symbol characteristics
            # In production, this would use historical data and ML models
            
            adjustments = {
                'price': 1.0,
                'volume': 0.8,
                'volatility': 1.2,
                'sentiment': 0.9
            }
            
            return adjustments.get(effect, 1.0)
            
        except Exception as e:
            logger.error(f"Market adjustment calculation failed: {e}")
            return 1.0

    async def _fallback_causal_analysis(self, symbol: str, intervention: Dict[str, float], 
                                      time_horizon: int) -> Dict[str, Any]:
        """Fallback causal analysis when libraries are not available"""
        try:
            # Simplified rule-based causal reasoning
            predicted_effects = {}
            
            for variable, new_value in intervention.items():
                if variable == 'sentiment' and new_value > 0:
                    predicted_effects['price'] = new_value * 0.1  # 10% of sentiment change
                elif variable == 'volume' and new_value > 0:
                    predicted_effects['volatility'] = new_value * 0.05  # 5% of volume change
                elif variable == 'interest_rates':
                    predicted_effects['price'] = -new_value * 0.2  # Inverse relationship
            
            return {
                'scenario_id': f"fallback_{symbol}_{int(datetime.now().timestamp())}",
                'intervention': intervention,
                'predicted_effects': predicted_effects,
                'confidence': 0.6,  # Lower confidence for fallback
                'time_horizon': time_horizon,
                'method': 'rule_based_fallback',
                'assumptions': ['Simplified causal relationships', 'No complex interactions'],
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Fallback causal analysis failed: {e}")
            return {'error': str(e)}

    def _cache_scenario(self, scenario: CausalScenario):
        """Cache scenario analysis results"""
        try:
            if len(self.scenario_cache) >= self.max_cache_size:
                # Remove oldest entries
                oldest_key = min(self.scenario_cache.keys(), 
                               key=lambda k: self.scenario_cache[k].timestamp)
                del self.scenario_cache[oldest_key]
            
            self.scenario_cache[scenario.scenario_id] = scenario
            
        except Exception as e:
            logger.error(f"Scenario caching failed: {e}")

    def _format_causal_response(self, scenario: CausalScenario) -> Dict[str, Any]:
        """Format causal analysis response"""
        return {
            'scenario_id': scenario.scenario_id,
            'intervention': scenario.intervention,
            'predicted_effects': scenario.predicted_effects,
            'confidence': scenario.confidence,
            'time_horizon': scenario.time_horizon,
            'assumptions': scenario.assumptions,
            'timestamp': scenario.timestamp.isoformat(),
            'method': 'causal_inference' if self.causal_libs_available else 'fallback'
        }

    async def get_causal_explanation(self, symbol: str, outcome: str) -> Dict[str, Any]:
        """Get causal explanation for observed market outcome"""
        try:
            if not self.causal_libs_available:
                return await self._fallback_explanation(symbol, outcome)
            
            # Find potential causes for the outcome
            potential_causes = []
            if self.causal_graph:
                # Find all predecessors (potential causes)
                predecessors = list(nx.predecessors(self.causal_graph, outcome))
                
                for cause in predecessors:
                    key = f"{cause}->{outcome}"
                    if key in self.relationships:
                        relationship = self.relationships[key]
                        potential_causes.append({
                            'cause': cause,
                            'strength': relationship.strength,
                            'confidence': relationship.confidence,
                            'time_lag': relationship.time_lag
                        })
            
            # Sort by strength
            potential_causes.sort(key=lambda x: x['strength'], reverse=True)

            base_explanation = {
                'symbol': symbol,
                'outcome': outcome,
                'potential_causes': potential_causes[:5],  # Top 5 causes
                'explanation_confidence': 0.8,
                'method': 'causal_graph_analysis',
                'timestamp': datetime.now().isoformat()
            }

            # Enhance with Grok reasoning if available
            if self.grok_engine:
                try:
                    # Create a mock intervention for explanation enhancement
                    mock_intervention = {outcome: 1.0}  # Assume the outcome occurred

                    enhanced_result = await self.grok_engine.enhance_causal_reasoning(
                        base_explanation, symbol, mock_intervention
                    )

                    # Combine original and enhanced explanations
                    enhanced_explanation = base_explanation.copy()
                    enhanced_explanation.update({
                        'grok_enhanced': True,
                        'enhanced_confidence': enhanced_result.combined_confidence,
                        'grok_explanation': enhanced_result.grok_enhancement.content if enhanced_result.grok_enhancement.success else None,
                        'reasoning_chain': enhanced_result.reasoning_chain,
                        'improvement_metrics': enhanced_result.improvement_metrics,
                        'original_confidence': base_explanation.get('explanation_confidence', 0.8)
                    })

                    logger.info(f"[GROK] Enhanced causal explanation for {symbol} outcome: {outcome}")
                    return enhanced_explanation

                except Exception as e:
                    logger.error(f"Grok enhancement failed for causal explanation: {e}")
                    base_explanation['grok_enhanced'] = False
                    base_explanation['grok_error'] = str(e)

            return base_explanation
            
        except Exception as e:
            logger.error(f"Causal explanation failed for {symbol}: {e}")
            return {'error': str(e)}

    async def _fallback_explanation(self, symbol: str, outcome: str) -> Dict[str, Any]:
        """Fallback explanation when causal libraries are not available"""
        # Simplified rule-based explanations
        explanations = {
            'price_increase': ['positive_earnings', 'positive_sentiment', 'high_volume'],
            'price_decrease': ['negative_earnings', 'negative_sentiment', 'high_volatility'],
            'volatility_spike': ['earnings_announcement', 'news_event', 'options_expiry']
        }
        
        potential_causes = explanations.get(outcome, ['market_dynamics'])
        
        return {
            'symbol': symbol,
            'outcome': outcome,
            'potential_causes': [{'cause': cause, 'strength': 0.6, 'confidence': 0.5} 
                               for cause in potential_causes],
            'explanation_confidence': 0.5,
            'method': 'rule_based_fallback',
            'timestamp': datetime.now().isoformat()
        }

    def get_engine_status(self) -> Dict[str, Any]:
        """Get engine status and statistics"""
        status = {
            'status': self.status.value,
            'causal_libs_available': self.causal_libs_available,
            'relationships_count': len(self.relationships),
            'cached_scenarios': len(self.scenario_cache),
            'market_variables': len(self.market_variables),
            'graph_nodes': len(self.causal_graph.nodes) if self.causal_graph else 0,
            'graph_edges': len(self.causal_graph.edges) if self.causal_graph else 0,
            'grok_integration_available': self.grok_integration_available,
            'grok_enhanced_results': len(self.grok_enhanced_results)
        }

        # Add Grok engine status if available
        if self.grok_engine:
            status['grok_engine_status'] = self.grok_engine.get_engine_status()

        return status

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasCausalReasoningEngine",
    "CausalRelationship", 
    "CausalScenario",
    "CausalRelationType"
]
