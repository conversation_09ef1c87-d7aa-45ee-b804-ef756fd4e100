{"timestamp": "2025-07-21T06:51:15.300617", "summary": {"total_tests": 6, "passed": 3, "success_rate": 50.0, "status": "PARTIAL"}, "detailed_results": {"reasoning": {"status": "FAIL", "error": "No valid response"}, "web_search": {"status": "ERROR", "error": "'GrokResponse' object has no attribute 'get'"}, "function_calling": {"status": "PASS", "tools_available": 4, "response_quality": 0.7920000000000001}, "sentiment_analysis": {"status": "PASS", "confidence": 0.6600000000000001, "analysis_quality": "high"}, "pattern_recognition": {"status": "PASS", "confidence": 0.7040000000000002, "patterns_identified": true}, "intent_detection": {"status": "FAIL", "error": "No intent response"}}, "grok_config": {"model": "grok-3-latest", "base_url": "https://api.x.ai/v1", "available": true}}