"""
A.T.L.A.S Real-time - Consolidated Real-time Scanning and Analysis
Combines Real-time Scanner, TTM Pattern Detector, and AI Enhanced Risk Management
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import settings
from models import EngineStatus
from sp500_symbols import get_sp500_symbols, get_core_sp500_symbols, get_high_volume_symbols

logger = logging.getLogger(__name__)


# ============================================================================
# REAL-TIME SCANNER
# ============================================================================

class AtlasRealtimeScanner:
    """Real-time market scanning and alert system"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        # Use full S&P 500 for comprehensive real-time scanning
        self.scan_symbols = get_sp500_symbols()
        self.scan_results = []
        self.alerts = []
        self.is_scanning = False
        
        logger.info("[SCANNER] Real-time Scanner initialized")

    async def initialize(self):
        """Initialize real-time scanner"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Initialize scan parameters
            self.scan_interval = 30  # seconds
            self.alert_thresholds = {
                'volume_spike': 2.0,  # 2x average volume
                'price_movement': 0.05,  # 5% price movement
                'volatility': 0.03  # 3% volatility threshold
            }
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Real-time Scanner ready")
            
        except Exception as e:
            logger.error(f"Real-time Scanner initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def start_scanning(self):
        """Start real-time scanning"""
        try:
            if self.is_scanning:
                return {"status": "already_running"}
            
            self.is_scanning = True
            logger.info("[SCANNER] Starting real-time market scanning")
            
            # Start scanning task
            asyncio.create_task(self._scanning_loop())
            
            return {"status": "started", "symbols": len(self.scan_symbols)}
            
        except Exception as e:
            logger.error(f"Failed to start scanning: {e}")
            return {"status": "error", "error": str(e)}

    async def stop_scanning(self):
        """Stop real-time scanning"""
        try:
            self.is_scanning = False
            logger.info("[SCANNER] Real-time scanning stopped")
            return {"status": "stopped"}
            
        except Exception as e:
            logger.error(f"Failed to stop scanning: {e}")
            return {"status": "error", "error": str(e)}

    async def _scanning_loop(self):
        """Main scanning loop"""
        while self.is_scanning:
            try:
                await self._perform_scan()
                await asyncio.sleep(self.scan_interval)
            except Exception as e:
                logger.error(f"Scanning loop error: {e}")
                await asyncio.sleep(5)

    async def _perform_scan(self):
        """Perform single scan cycle"""
        try:
            scan_results = []
            
            for symbol in self.scan_symbols:
                result = await self._scan_symbol(symbol)
                if result:
                    scan_results.append(result)
            
            # Store results
            self.scan_results.append({
                'timestamp': datetime.now().isoformat(),
                'results': scan_results,
                'symbols_scanned': len(self.scan_symbols)
            })
            
            # Keep only recent results
            if len(self.scan_results) > 100:
                self.scan_results = self.scan_results[-100:]
            
            logger.info(f"[SCAN] Completed scan: {len(scan_results)} alerts found")
            
        except Exception as e:
            logger.error(f"Scan performance failed: {e}")

    async def _scan_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Scan individual symbol for alerts"""
        try:
            # Simulate real-time data analysis
            import random
            
            # Generate simulated market data
            price_change = random.uniform(-0.08, 0.08)  # ±8% change
            volume_ratio = random.uniform(0.5, 3.0)  # Volume ratio
            volatility = random.uniform(0.01, 0.06)  # Volatility
            
            alerts = []
            
            # Check for volume spike
            if volume_ratio > self.alert_thresholds['volume_spike']:
                alerts.append({
                    'type': 'volume_spike',
                    'message': f"Volume spike detected: {volume_ratio:.1f}x average",
                    'severity': 'medium'
                })
            
            # Check for significant price movement
            if abs(price_change) > self.alert_thresholds['price_movement']:
                direction = 'up' if price_change > 0 else 'down'
                alerts.append({
                    'type': 'price_movement',
                    'message': f"Significant price movement {direction}: {abs(price_change)*100:.1f}%",
                    'severity': 'high' if abs(price_change) > 0.07 else 'medium'
                })
            
            # Check for high volatility
            if volatility > self.alert_thresholds['volatility']:
                alerts.append({
                    'type': 'high_volatility',
                    'message': f"High volatility detected: {volatility*100:.1f}%",
                    'severity': 'medium'
                })
            
            if alerts:
                return {
                    'symbol': symbol,
                    'timestamp': datetime.now().isoformat(),
                    'price_change': price_change,
                    'volume_ratio': volume_ratio,
                    'volatility': volatility,
                    'alerts': alerts
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Symbol scan failed for {symbol}: {e}")
            return None

    def get_latest_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get latest alerts"""
        try:
            all_alerts = []
            
            # Collect alerts from recent scans
            for scan in self.scan_results[-10:]:  # Last 10 scans
                for result in scan['results']:
                    for alert in result['alerts']:
                        all_alerts.append({
                            'symbol': result['symbol'],
                            'timestamp': result['timestamp'],
                            'alert': alert
                        })
            
            # Sort by timestamp and return latest
            all_alerts.sort(key=lambda x: x['timestamp'], reverse=True)
            return all_alerts[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get latest alerts: {e}")
            return []


# ============================================================================
# TTM PATTERN DETECTOR
# ============================================================================

class AtlasTTMPatternDetector:
    """TTM Squeeze pattern detection system"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.pattern_cache = {}
        self.detection_history = []
        
        logger.info("[TTM] TTM Pattern Detector initialized")

    async def initialize(self):
        """Initialize TTM pattern detector"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # TTM Squeeze parameters
            self.ttm_params = {
                'bb_period': 20,
                'bb_std': 2.0,
                'kc_period': 20,
                'kc_multiplier': 1.5,
                'momentum_period': 12
            }
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] TTM Pattern Detector ready")
            
        except Exception as e:
            logger.error(f"TTM Pattern Detector initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def detect_ttm_squeeze(self, symbol: str) -> Dict[str, Any]:
        """Detect TTM Squeeze pattern for symbol"""
        try:
            # Simulate TTM Squeeze detection
            import random
            
            # Generate simulated squeeze conditions
            bb_inside_kc = random.choice([True, False])  # Bollinger Bands inside Keltner Channels
            momentum_direction = random.choice(['bullish', 'bearish', 'neutral'])
            squeeze_strength = random.uniform(0.3, 0.9)
            
            # Determine squeeze status
            if bb_inside_kc:
                squeeze_status = 'active'
                signal_strength = 'strong' if squeeze_strength > 0.7 else 'medium'
            else:
                squeeze_status = 'fired'
                signal_strength = 'weak'
            
            pattern_result = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'squeeze_status': squeeze_status,
                'momentum_direction': momentum_direction,
                'signal_strength': signal_strength,
                'squeeze_strength': squeeze_strength,
                'bb_inside_kc': bb_inside_kc,
                'recommendation': self._generate_ttm_recommendation(
                    squeeze_status, momentum_direction, signal_strength
                )
            }
            
            # Cache result
            self.pattern_cache[symbol] = pattern_result
            
            # Store in history
            self.detection_history.append(pattern_result)
            if len(self.detection_history) > 1000:
                self.detection_history = self.detection_history[-1000:]
            
            return pattern_result
            
        except Exception as e:
            logger.error(f"TTM Squeeze detection failed for {symbol}: {e}")
            return {'error': str(e)}

    def _generate_ttm_recommendation(self, squeeze_status: str, momentum_direction: str, 
                                   signal_strength: str) -> Dict[str, Any]:
        """Generate trading recommendation based on TTM pattern"""
        try:
            if squeeze_status == 'active':
                if momentum_direction == 'bullish' and signal_strength in ['strong', 'medium']:
                    return {
                        'action': 'prepare_long',
                        'message': 'TTM Squeeze building - prepare for bullish breakout',
                        'confidence': 0.7 if signal_strength == 'strong' else 0.6
                    }
                elif momentum_direction == 'bearish' and signal_strength in ['strong', 'medium']:
                    return {
                        'action': 'prepare_short',
                        'message': 'TTM Squeeze building - prepare for bearish breakdown',
                        'confidence': 0.7 if signal_strength == 'strong' else 0.6
                    }
                else:
                    return {
                        'action': 'wait',
                        'message': 'TTM Squeeze active but direction unclear - wait for confirmation',
                        'confidence': 0.5
                    }
            else:  # squeeze_status == 'fired'
                return {
                    'action': 'monitor',
                    'message': 'TTM Squeeze has fired - monitor for follow-through',
                    'confidence': 0.4
                }
                
        except Exception as e:
            logger.error(f"TTM recommendation generation failed: {e}")
            return {
                'action': 'wait',
                'message': 'Unable to generate recommendation',
                'confidence': 0.0
            }


# ============================================================================
# AI ENHANCED RISK MANAGEMENT
# ============================================================================

class AtlasAIEnhancedRiskManagement:
    """AI-enhanced real-time risk management"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.risk_alerts = []
        self.risk_models = {}
        
        logger.info("[AI-RISK] AI Enhanced Risk Management initialized")

    async def initialize(self):
        """Initialize AI risk management"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Risk thresholds
            self.risk_thresholds = {
                'portfolio_var': 0.05,  # 5% VaR limit
                'position_concentration': 0.10,  # 10% max position
                'correlation_limit': 0.7,  # Max correlation
                'volatility_limit': 0.30  # 30% volatility limit
            }
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] AI Enhanced Risk Management ready")
            
        except Exception as e:
            logger.error(f"AI Risk Management initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def assess_realtime_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess real-time portfolio risk"""
        try:
            # Simulate AI risk assessment
            import random
            
            # Calculate risk metrics
            portfolio_var = random.uniform(0.02, 0.08)  # Portfolio VaR
            max_position = random.uniform(0.05, 0.15)  # Largest position
            avg_correlation = random.uniform(0.3, 0.8)  # Average correlation
            portfolio_volatility = random.uniform(0.15, 0.35)  # Portfolio volatility
            
            # Check risk violations
            violations = []
            
            if portfolio_var > self.risk_thresholds['portfolio_var']:
                violations.append({
                    'type': 'var_exceeded',
                    'message': f"Portfolio VaR exceeded: {portfolio_var:.1%} > {self.risk_thresholds['portfolio_var']:.1%}",
                    'severity': 'high'
                })
            
            if max_position > self.risk_thresholds['position_concentration']:
                violations.append({
                    'type': 'concentration_risk',
                    'message': f"Position concentration too high: {max_position:.1%}",
                    'severity': 'medium'
                })
            
            if avg_correlation > self.risk_thresholds['correlation_limit']:
                violations.append({
                    'type': 'high_correlation',
                    'message': f"Portfolio correlation too high: {avg_correlation:.1%}",
                    'severity': 'medium'
                })
            
            if portfolio_volatility > self.risk_thresholds['volatility_limit']:
                violations.append({
                    'type': 'high_volatility',
                    'message': f"Portfolio volatility elevated: {portfolio_volatility:.1%}",
                    'severity': 'low'
                })
            
            # Calculate risk score
            risk_score = 100
            risk_score -= len(violations) * 20
            risk_score -= (portfolio_var / self.risk_thresholds['portfolio_var'] - 1) * 30
            risk_score = max(0, risk_score)
            
            # Determine risk level
            if risk_score >= 80:
                risk_level = 'low'
            elif risk_score >= 60:
                risk_level = 'medium'
            elif risk_score >= 40:
                risk_level = 'high'
            else:
                risk_level = 'critical'
            
            risk_assessment = {
                'timestamp': datetime.now().isoformat(),
                'risk_score': round(risk_score, 1),
                'risk_level': risk_level,
                'metrics': {
                    'portfolio_var': portfolio_var,
                    'max_position': max_position,
                    'avg_correlation': avg_correlation,
                    'portfolio_volatility': portfolio_volatility
                },
                'violations': violations,
                'recommendations': self._generate_risk_recommendations(violations, risk_level)
            }
            
            # Store risk alert if needed
            if violations:
                self.risk_alerts.append(risk_assessment)
                if len(self.risk_alerts) > 100:
                    self.risk_alerts = self.risk_alerts[-100:]
            
            return risk_assessment
            
        except Exception as e:
            logger.error(f"Real-time risk assessment failed: {e}")
            return {'error': str(e)}

    def _generate_risk_recommendations(self, violations: List[Dict[str, Any]], 
                                     risk_level: str) -> List[str]:
        """Generate risk management recommendations"""
        try:
            recommendations = []
            
            for violation in violations:
                if violation['type'] == 'var_exceeded':
                    recommendations.append("Consider reducing position sizes or hedging portfolio")
                elif violation['type'] == 'concentration_risk':
                    recommendations.append("Diversify portfolio by reducing largest positions")
                elif violation['type'] == 'high_correlation':
                    recommendations.append("Add uncorrelated assets to reduce portfolio correlation")
                elif violation['type'] == 'high_volatility':
                    recommendations.append("Consider defensive positions or volatility hedging")
            
            if risk_level == 'critical':
                recommendations.append("URGENT: Consider immediate risk reduction measures")
            elif risk_level == 'high':
                recommendations.append("Review and adjust portfolio allocation")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Risk recommendation generation failed: {e}")
            return ["Unable to generate recommendations"]


# ============================================================================
# REAL-TIME ORCHESTRATOR
# ============================================================================

class AtlasRealtimeOrchestrator:
    """Main real-time orchestrator"""
    
    def __init__(self):
        self.realtime_scanner = AtlasRealtimeScanner()
        self.ttm_detector = AtlasTTMPatternDetector()
        self.ai_risk_manager = AtlasAIEnhancedRiskManagement()
        self.status = EngineStatus.INITIALIZING
        
        logger.info("[ORCHESTRATOR] Real-time Orchestrator initialized")

    async def initialize(self):
        """Initialize all real-time components"""
        try:
            await self.realtime_scanner.initialize()
            await self.ttm_detector.initialize()
            await self.ai_risk_manager.initialize()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Real-time Orchestrator fully initialized")
            
        except Exception as e:
            logger.error(f"Real-time Orchestrator initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def start_realtime_monitoring(self):
        """Start all real-time monitoring"""
        try:
            await self.realtime_scanner.start_scanning()
            logger.info("[REALTIME] All real-time monitoring started")
            return {"status": "started"}
            
        except Exception as e:
            logger.error(f"Failed to start real-time monitoring: {e}")
            return {"status": "error", "error": str(e)}

    async def stop_realtime_monitoring(self):
        """Stop all real-time monitoring"""
        try:
            await self.realtime_scanner.stop_scanning()
            logger.info("[REALTIME] All real-time monitoring stopped")
            return {"status": "stopped"}
            
        except Exception as e:
            logger.error(f"Failed to stop real-time monitoring: {e}")
            return {"status": "error", "error": str(e)}


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasRealtimeScanner",
    "AtlasTTMPatternDetector",
    "AtlasAIEnhancedRiskManagement",
    "AtlasRealtimeOrchestrator"
]
