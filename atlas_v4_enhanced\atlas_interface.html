<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. Trading System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #0f0f23;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            display: flex;
            height: 100vh;
        }

        /* Left Scanner Panel */
        .scanner-panel {
            width: 400px;
            background: #1a1a2e;
            border-right: 1px solid #16213e;
            display: flex;
            flex-direction: column;
        }

        .scanner-header {
            padding: 20px;
            border-bottom: 1px solid #16213e;
            background: #16213e;
        }

        .scanner-title {
            font-size: 18px;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 8px;
        }

        .scanner-subtitle {
            font-size: 14px;
            color: #8892b0;
        }

        .scanner-stats {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            background: #0f0f23;
            border-bottom: 1px solid #16213e;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #00ff88;
        }

        .stat-label {
            font-size: 12px;
            color: #8892b0;
            margin-top: 4px;
        }

        .scanner-controls {
            padding: 15px 20px;
            border-bottom: 1px solid #16213e;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #00ff88;
            color: #0f0f23;
        }

        .btn-primary:hover {
            background: #00cc6a;
        }

        .btn-secondary {
            background: #16213e;
            color: #ffffff;
            border: 1px solid #8892b0;
        }

        .btn-secondary:hover {
            background: #1e2a4a;
        }

        .scanner-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .signal-item {
            padding: 15px 20px;
            border-bottom: 1px solid #16213e;
            cursor: pointer;
            transition: background 0.2s;
        }

        .signal-item:hover {
            background: #16213e;
        }

        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .signal-symbol {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .signal-price {
            font-size: 14px;
            color: #00ff88;
        }

        .signal-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .signal-confidence {
            font-size: 12px;
            color: #8892b0;
        }

        .signal-strength {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .strength-high {
            background: #00ff88;
            color: #0f0f23;
        }

        .strength-medium {
            background: #ffa500;
            color: #0f0f23;
        }

        .strength-low {
            background: #ff6b6b;
            color: #ffffff;
        }

        /* Right Chat Panel */
        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #0f0f23;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #16213e;
            background: #16213e;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .chat-subtitle {
            font-size: 14px;
            color: #8892b0;
        }

        .data-status-bar {
            margin-top: 8px;
            padding: 6px 12px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            border-left: 3px solid #ffa500;
        }

        .data-status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .data-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ffa500;
            animation: pulse 2s infinite;
        }

        .data-status-dot.healthy {
            background: #4CAF50;
            animation: none;
        }

        .data-status-dot.warning {
            background: #ff9800;
            animation: pulse 1s infinite;
        }

        .data-status-dot.critical {
            background: #f44336;
            animation: blink 0.5s infinite;
        }

        .data-status-text {
            font-size: 11px;
            color: #ccc;
            font-weight: 500;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        /* Intent Detection Bubble - ChatGPT Style */
        .intent-detection-bubble {
            position: fixed;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.85);
            color: #fff;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            display: none;
            align-items: center;
            gap: 8px;
            z-index: 1000;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            animation: slideUpFade 0.3s ease-out;
            max-width: 300px;
            text-align: center;
        }

        .intent-detection-bubble.show {
            display: flex;
        }

        .intent-bubble-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            animation: pulse 2s infinite;
        }

        .intent-bubble-text {
            color: #fff;
            font-size: 13px;
            font-weight: 500;
        }

        .intent-bubble-category {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            margin-left: 4px;
        }

        @keyframes slideUpFade {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        /* Intent-specific styling */
        .intent-bubble-stock-analysis {
            background: rgba(76, 175, 80, 0.9);
        }

        .intent-bubble-trading {
            background: rgba(255, 152, 0, 0.9);
        }

        .intent-bubble-risk {
            background: rgba(244, 67, 54, 0.9);
        }

        .intent-bubble-prediction {
            background: rgba(156, 39, 176, 0.9);
        }

        .intent-bubble-news {
            background: rgba(33, 150, 243, 0.9);
        }

        .intent-bubble-education {
            background: rgba(96, 125, 139, 0.9);
        }

        .intent-bubble-options {
            background: rgba(255, 193, 7, 0.9);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.5;
        }

        .message-user {
            align-self: flex-end;
            background: #00ff88;
            color: #0f0f23;
        }

        .message-bot {
            align-self: flex-start;
            background: #1a1a2e;
            color: #ffffff;
            border: 1px solid #16213e;
        }

        .message-loading {
            align-self: flex-start;
            background: #1a1a2e;
            color: #8892b0;
            border: 1px solid #16213e;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .loading-dots {
            display: flex;
            gap: 4px;
        }

        .loading-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #8892b0;
            animation: loading 1.4s infinite ease-in-out;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .chat-input-container {
            padding: 20px;
            border-top: 1px solid #16213e;
            background: #1a1a2e;
        }

        .chat-input-wrapper {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #16213e;
            border-radius: 8px;
            background: #0f0f23;
            color: #ffffff;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            font-family: inherit;
        }

        .chat-input:focus {
            outline: none;
            border-color: #00ff88;
        }

        .chat-input::placeholder {
            color: #8892b0;
        }

        .send-button {
            padding: 12px 16px;
            background: #00ff88;
            color: #0f0f23;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }

        .send-button:hover:not(:disabled) {
            background: #00cc6a;
        }

        .send-button:disabled {
            background: #16213e;
            color: #8892b0;
            cursor: not-allowed;
        }

        .connection-status {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            background: rgba(0, 255, 136, 0.1);
            color: #00ff88;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .status-disconnected {
            background: rgba(255, 107, 107, 0.1);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #0f0f23;
        }

        ::-webkit-scrollbar-thumb {
            background: #16213e;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #1e2a4a;
        }

        /* Progress Indicator Styles */
        .message-progress {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            border: 1px solid #00ff8830;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 0;
        }

        .progress-container {
            width: 100%;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-title {
            font-size: 14px;
            font-weight: 600;
            color: #00ff88;
        }

        .progress-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            font-weight: 500;
        }

        .progress-status.status-pending {
            background: #ffa50020;
            color: #ffa500;
        }

        .progress-status.status-in_progress {
            background: #00bfff20;
            color: #00bfff;
        }

        .progress-status.status-completed {
            background: #00ff8820;
            color: #00ff88;
        }

        .progress-status.status-failed {
            background: #ff444420;
            color: #ff4444;
        }

        .progress-description {
            font-size: 12px;
            color: #8892b0;
            margin-bottom: 12px;
        }

        .progress-bar-container {
            width: 100%;
            height: 6px;
            background: #0f0f23;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #00ff88, #00bfff);
            border-radius: 3px;
            transition: width 0.5s ease-in-out;
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-steps {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .progress-step {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 0;
            font-size: 11px;
        }

        .step-icon {
            font-size: 14px;
            width: 20px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        .step-icon.step-in_progress {
            animation: spin 1s linear infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .step-content {
            flex: 1;
        }

        .step-detail {
            font-size: 10px;
            color: #4CAF50;
            font-style: italic;
            padding: 2px 6px;
            background: rgba(76, 175, 80, 0.1);
            border-radius: 3px;
            margin-top: 2px;
            display: inline-block;
        }

        .step-name {
            font-weight: 500;
            color: #ffffff;
        }

        .step-description {
            color: #8892b0;
            font-size: 10px;
        }

        .step-progress {
            font-size: 10px;
            color: #00ff88;
            font-weight: 500;
            min-width: 35px;
            text-align: right;
        }

        .progress-step.step-pending {
            opacity: 0.6;
        }

        .progress-step.step-in_progress {
            opacity: 1;
            background: rgba(0, 191, 255, 0.1);
            border-radius: 4px;
            padding: 6px 8px;
        }

        .progress-step.step-completed {
            opacity: 0.8;
        }

        .progress-step.step-failed {
            opacity: 0.8;
            color: #ff4444;
        }

        /* System Status Dashboard Styles */
        .system-status-panel {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 20px;
            overflow-y: auto;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #16213e;
        }

        .status-header h3 {
            color: #00ff88;
            font-size: 18px;
            margin: 0;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-item {
            background: #1a1a2e;
            border: 1px solid #16213e;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }

        .status-label {
            font-size: 11px;
            color: #8892b0;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-value {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .status-value.status-healthy {
            color: #00ff88;
        }

        .status-value.status-warning {
            color: #ffa500;
        }

        .status-value.status-error {
            color: #ff4444;
        }

        .status-alerts {
            background: #1a1a2e;
            border: 1px solid #16213e;
            border-radius: 8px;
            padding: 15px;
        }

        .alerts-header {
            font-size: 14px;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 10px;
        }

        .alerts-list {
            max-height: 150px;
            overflow-y: auto;
        }

        .alert-item {
            padding: 8px 0;
            border-bottom: 1px solid #16213e;
            font-size: 12px;
        }

        .alert-item:last-child {
            border-bottom: none;
        }

        .alert-level-critical {
            color: #ff4444;
        }

        .alert-level-error {
            color: #ff6b6b;
        }

        .alert-level-warning {
            color: #ffa500;
        }

        .alert-level-info {
            color: #8892b0;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <div class="status-dot"></div>
        <span>Connecting...</span>
    </div>

    <div class="main-container">
        <!-- Left Scanner Panel -->
        <div class="scanner-panel">
            <div class="scanner-header">
                <div class="scanner-title">Lee Method Scanner</div>
                <div class="scanner-subtitle">Live S&P 500 Pattern Detection</div>
            </div>

            <div class="scanner-stats">
                <div class="stat-item">
                    <div class="stat-value" id="activeSignals">0</div>
                    <div class="stat-label">Active Signals</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="patternAccuracy">87%</div>
                    <div class="stat-label">Pattern Accuracy</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="scansToday">1,247</div>
                    <div class="stat-label">Scans Today</div>
                </div>
            </div>

            <div class="scanner-controls">
                <div class="control-buttons">
                    <button class="btn btn-primary" id="refreshScanBtn">🔄 Refresh</button>
                    <button class="btn btn-secondary" id="configScanBtn">⚙️ Config</button>
                    <button class="btn btn-secondary" id="viewCriteriaBtn">📋 Criteria</button>
                    <button class="btn btn-secondary" id="systemStatusBtn">📊 Status</button>
                </div>
            </div>

            <!-- System Status Dashboard -->
            <div class="system-status-panel" id="systemStatusPanel" style="display: none;">
                <div class="status-header">
                    <h3>System Status Dashboard</h3>
                    <button class="btn btn-small" id="closeStatusBtn">✕</button>
                </div>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-label">Market Hours</div>
                        <div class="status-value" id="marketHoursStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Scanner Status</div>
                        <div class="status-value" id="scannerStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">AI Systems</div>
                        <div class="status-value" id="aiSystemsStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Data Sources</div>
                        <div class="status-value" id="dataSourcesStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Response Time</div>
                        <div class="status-value" id="responseTimeStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Success Rate</div>
                        <div class="status-value" id="successRateStatus">--</div>
                    </div>
                </div>
                <div class="status-alerts" id="statusAlerts">
                    <div class="alerts-header">Recent Alerts</div>
                    <div class="alerts-list" id="alertsList">No recent alerts</div>
                </div>
            </div>

            <div class="scanner-content" id="scannerResults">
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Loading signals...</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">Initializing scanner...</div>
                        <div class="signal-strength strength-medium">LOADING</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Chat Panel -->
        <div class="chat-panel">
            <div class="chat-header">
                <div class="chat-title">A.T.L.A.S. AI Assistant</div>
                <div class="chat-subtitle">Advanced Trading & Learning Analysis System</div>
                <div class="data-status-bar" id="dataStatusBar">
                    <div class="data-status-indicator" id="dataStatusIndicator">
                        <span class="data-status-dot" id="dataStatusDot"></span>
                        <span class="data-status-text" id="dataStatusText">Checking Data Sources...</span>
                    </div>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message message-bot">
                    <strong>A.T.L.A.S. AI:</strong> Welcome to the Advanced Trading & Learning Analysis System! I have access to all 25+ A.T.L.A.S. capabilities including live trading, Lee Method pattern detection, ML predictions, sentiment analysis, technical analysis, and comprehensive trading education. How can I assist you today?
                </div>
            </div>

            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea
                        class="chat-input"
                        id="chatInput"
                        placeholder="Ask A.T.L.A.S. about trading strategies, market analysis, or any trading question..."
                        rows="1"
                    ></textarea>
                    <button class="send-button" id="sendButton">Send</button>
                </div>
            </div>

            <!-- Intent Detection Bubble - ChatGPT Style -->
            <div id="intentDetectionBubble" class="intent-detection-bubble">
                <div class="intent-bubble-icon" id="intentBubbleIcon">🧠</div>
                <div class="intent-bubble-text" id="intentBubbleText">Analyzing your request...</div>
                <div class="intent-bubble-category" id="intentBubbleCategory">General</div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let websocket = null;
        let isConnected = false;
        let sessionId = 'web_' + Date.now();
        let activeOperations = new Map();
        let progressContainer = null;

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, initializing A.T.L.A.S. interface...');

            // Clear only genuinely stuck messages after a delay
            setTimeout(() => {
                clearStuckProgressMessages();
                console.log('✅ Interface cleanup completed');
            }, 2000); // Wait 2 seconds to avoid clearing legitimate progress messages

            initializeInterface();
            initializeWebSocket();
            startPeriodicCleanup(); // Start periodic cleanup to prevent stuck states
        });

        // Add a global function to manually clear stuck messages (for debugging)
        window.forceCleanInterface = function() {
            console.log('🧹 Force cleaning interface...');
            clearStuckProgressMessages();

            // Also clear any loading states
            const sendButton = document.getElementById('send-button');
            if (sendButton) {
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
            }

            // Clear input if needed
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.disabled = false;
            }

            console.log('✅ Interface force cleaned');
        };

        // WebSocket initialization for real-time updates
        function initializeWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const port = window.location.port || (protocol === 'wss:' ? '443' : '80');
            const wsUrl = `${protocol}//${window.location.hostname}:${port}/ws/${sessionId}`;

            console.log('🔗 Attempting WebSocket connection to:', wsUrl);

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    console.log('🔗 WebSocket connected for real-time updates');
                    isConnected = true;
                    updateConnectionStatus(true);
                };

                websocket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };

                websocket.onclose = function(event) {
                    console.log('🔌 WebSocket disconnected');
                    isConnected = false;
                    updateConnectionStatus(false);

                    // Attempt to reconnect after 3 seconds
                    setTimeout(initializeWebSocket, 3000);
                };

                websocket.onerror = function(error) {
                    console.error('❌ WebSocket error:', error);
                    isConnected = false;
                    updateConnectionStatus(false);

                    // Show user-friendly error message
                    const chatMessages = document.getElementById('chatMessages');
                    if (chatMessages) {
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'message system-message';
                        errorDiv.innerHTML = `
                            <div style="color: #ff6b6b; padding: 10px; border: 1px solid #ff6b6b; border-radius: 5px; margin: 5px 0;">
                                ⚠️ Real-time updates disconnected. Trying to reconnect...
                            </div>
                        `;
                        chatMessages.appendChild(errorDiv);
                        scrollToBottom();
                    }
                };

            } catch (error) {
                console.error('❌ Failed to initialize WebSocket:', error);
            }
        }

        // Handle WebSocket messages
        function handleWebSocketMessage(data) {
            console.log('📨 WebSocket message received:', data);

            switch (data.type) {
                case 'connection_established':
                    console.log('✅ Real-time connection established');
                    break;

                case 'progress_update':
                    if (data.operation && data.operation.progress_id) {
                        updateProgressMessage(
                            data.operation.progress_id,
                            data.operation.title || 'Processing...',
                            data.operation.description || 'Working on your request'
                        );
                    }
                    break;

                case 'chat_progress':
                    // Handle chat-specific progress updates
                    if (data.progress_id && data.title) {
                        updateProgressMessage(data.progress_id, data.title, data.description || '');
                    }
                    break;

                case 'operations_list':
                    displayOperationsList(data.operations);
                    break;

                case 'scanner_update':
                    // Handle real-time scanner updates
                    if (data.results) {
                        updateScannerResults(data.results);
                    }
                    break;

                default:
                    console.log('📨 Received WebSocket message:', data);
            }
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            // Add connection status indicator if it doesn't exist
            let statusIndicator = document.getElementById('connectionStatus');
            if (!statusIndicator) {
                statusIndicator = document.createElement('div');
                statusIndicator.id = 'connectionStatus';
                statusIndicator.style.cssText = `
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    padding: 5px 10px;
                    border-radius: 15px;
                    font-size: 12px;
                    font-weight: 500;
                    z-index: 1000;
                    transition: all 0.3s ease;
                `;
                document.body.appendChild(statusIndicator);
            }

            if (connected) {
                statusIndicator.textContent = '🟢 Live Updates';
                statusIndicator.style.background = '#00ff8820';
                statusIndicator.style.color = '#00ff88';
                statusIndicator.style.border = '1px solid #00ff88';
            } else {
                statusIndicator.textContent = '🔴 Reconnecting...';
                statusIndicator.style.background = '#ff444420';
                statusIndicator.style.color = '#ff4444';
                statusIndicator.style.border = '1px solid #ff4444';
            }
        }

        function initializeInterface() {
            console.log('🚀 Initializing A.T.L.A.S. Interface...');

            // Set up event listeners
            setupEventListeners();

            // Initialize WebSocket connection
            initializeWebSocket();

            // Load initial data
            loadInitialData();

            // Set up auto-refresh
            setupAutoRefresh();
        }

        function clearStuckProgressMessages() {
            // Only clear messages that are genuinely stuck (older than 5 minutes and marked as completed or failed)
            const progressMessages = document.querySelectorAll('.message-progress');
            const now = Date.now();

            progressMessages.forEach(msg => {
                const timestamp = msg.dataset.timestamp;
                const status = msg.dataset.status;

                // Only remove if message is very old (5+ minutes) OR marked as completed/failed
                if (timestamp && (
                    (now - parseInt(timestamp)) > 300000 || // 5 minutes
                    status === 'completed' ||
                    status === 'failed'
                )) {
                    console.log('🧹 Clearing stuck progress message:', msg.id, 'Status:', status);
                    msg.remove();
                }
            });
        }

        // Periodic cleanup to prevent stuck states (much less aggressive)
        function startPeriodicCleanup() {
            setInterval(() => {
                // Only check for messages older than 10 minutes that are truly stuck
                const progressMessages = document.querySelectorAll('.message-progress');
                const now = Date.now();

                progressMessages.forEach(msg => {
                    const timestamp = msg.dataset.timestamp;
                    const status = msg.dataset.status;

                    // Only remove if very old AND not actively processing
                    if (timestamp && status !== 'processing' && (now - parseInt(timestamp)) > 600000) { // 10 minutes
                        console.log('🧹 Removing very old progress message:', msg.id);
                        msg.remove();
                    }
                });
            }, 60000); // Check every minute instead of every 10 seconds
        }

        function setupEventListeners() {
            // Chat input handling
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');

            chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            chatInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            sendButton.addEventListener('click', sendMessage);

            // Scanner controls
            document.getElementById('refreshScanBtn').addEventListener('click', refreshScanner);
            document.getElementById('configScanBtn').addEventListener('click', showScannerConfig);
            document.getElementById('viewCriteriaBtn').addEventListener('click', showLeeMethodCriteria);
            document.getElementById('systemStatusBtn').addEventListener('click', showSystemStatus);
            document.getElementById('closeStatusBtn').addEventListener('click', hideSystemStatus);
        }

        function initializeWebSocket() {
            console.log('🔌 Initializing WebSocket connection...');

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/scanner`;

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    console.log('✅ WebSocket connected');
                    isConnected = true;
                    updateConnectionStatus(true);
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (error) {
                        console.error('❌ Error parsing WebSocket message:', error);
                    }
                };

                websocket.onclose = function(event) {
                    console.log('🔌 WebSocket disconnected');
                    isConnected = false;
                    updateConnectionStatus(false);

                    // Attempt to reconnect after 3 seconds
                    setTimeout(initializeWebSocket, 3000);
                };

                websocket.onerror = function(error) {
                    console.error('❌ WebSocket error:', error);
                    isConnected = false;
                    updateConnectionStatus(false);
                };

            } catch (error) {
                console.error('❌ Failed to initialize WebSocket:', error);
                updateConnectionStatus(false);
            }
        }

        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');

            if (connected) {
                statusElement.className = 'connection-status status-connected';
                statusElement.innerHTML = '<div class="status-dot"></div><span>Connected</span>';
            } else {
                statusElement.className = 'connection-status status-disconnected';
                statusElement.innerHTML = '<div class="status-dot"></div><span>Disconnected</span>';
            }
        }

        function handleWebSocketMessage(data) {
            console.log('📨 WebSocket message received:', data);

            if (data.type === 'scanner_update') {
                // Handle individual scanner result from WebSocket
                if (data.data && data.data.pattern_found) {
                    // Convert single result to array format for display
                    const signal = {
                        symbol: data.data.symbol,
                        price: data.data.price,
                        confidence: data.data.confidence,
                        pattern_type: 'Lee Method',
                        timestamp: new Date().toISOString()
                    };

                    // Add to existing signals or create new array
                    const existingSignals = getCurrentSignals();
                    const updatedSignals = [signal, ...existingSignals.slice(0, 19)]; // Keep latest 20

                    updateScannerDisplay(updatedSignals);

                    // Update stats with new signal count
                    const stats = {
                        active_signals: updatedSignals.length,
                        pattern_accuracy: 87,
                        scans_today: 1247
                    };
                    updateScannerStats(stats);
                }
            } else if (data.type === 'ping') {
                // Send pong response
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    websocket.send(JSON.stringify({ type: 'pong' }));
                }
            }
        }

        async function loadInitialData() {
            console.log('📊 Loading initial data...');

            // Test API connectivity
            await testApiConnectivity();

            // Load Lee Method signals
            await loadLeeMethodSignals();

            // Load scanner statistics
            await loadScannerStats();
        }

        async function testApiConnectivity() {
            try {
                const response = await fetch('/api/v1/health');
                const data = await response.json();

                if (data.status === 'healthy') {
                    console.log('✅ API connectivity verified');
                    updateConnectionStatus(true);
                } else {
                    console.warn('⚠️ API health check failed');
                }
            } catch (error) {
                console.error('❌ API connectivity test failed:', error);
                updateConnectionStatus(false);
            }
        }

        async function loadLeeMethodSignals() {
            try {
                console.log('📈 Loading Lee Method signals...');

                const response = await fetch('/api/v1/lee_method/signals');
                const data = await response.json();

                if (data.success && data.signals) {
                    updateScannerDisplay(data.signals);
                    console.log(`✅ Loaded ${data.signals.length} Lee Method signals`);
                } else {
                    console.warn('⚠️ No Lee Method signals available');
                    showNoSignalsMessage();
                }
            } catch (error) {
                console.error('❌ Error loading Lee Method signals:', error);
                showErrorMessage('Failed to load scanner signals');
            }
        }

        async function loadScannerStats() {
            try {
                const response = await fetch('/api/v1/lee_method/stats');
                const data = await response.json();

                if (data.success) {
                    updateScannerStats(data.stats);
                }
            } catch (error) {
                console.error('❌ Error loading scanner stats:', error);
            }
        }

        function updateScannerDisplay(signals) {
            const scannerResults = document.getElementById('scannerResults');

            if (!signals || signals.length === 0) {
                showNoSignalsMessage();
                return;
            }

            let html = '';
            signals.forEach(signal => {
                const strengthClass = getStrengthClass(signal.confidence);
                const strengthText = getStrengthText(signal.confidence);

                html += `
                    <div class="signal-item" onclick="showSignalDetails('${signal.symbol}')">
                        <div class="signal-header">
                            <div class="signal-symbol">${signal.symbol}</div>
                            <div class="signal-price">$${signal.price ? signal.price.toFixed(2) : '--'}</div>
                        </div>
                        <div class="signal-details">
                            <div class="signal-confidence">Confidence: ${(signal.confidence * 100).toFixed(1)}%</div>
                            <div class="signal-strength ${strengthClass}">${strengthText}</div>
                        </div>
                    </div>
                `;
            });

            scannerResults.innerHTML = html;

            // Update active signals count
            document.getElementById('activeSignals').textContent = signals.length;
        }

        function updateScannerStats(stats) {
            if (stats) {
                if (stats.active_signals !== undefined) {
                    document.getElementById('activeSignals').textContent = stats.active_signals;
                }
                if (stats.pattern_accuracy !== undefined) {
                    document.getElementById('patternAccuracy').textContent = `${(stats.pattern_accuracy * 100).toFixed(0)}%`;
                }
                if (stats.scans_today !== undefined) {
                    document.getElementById('scansToday').textContent = stats.scans_today.toLocaleString();
                }
            }
        }

        function getStrengthClass(confidence) {
            if (confidence >= 0.8) return 'strength-high';
            if (confidence >= 0.6) return 'strength-medium';
            return 'strength-low';
        }

        function getStrengthText(confidence) {
            if (confidence >= 0.8) return 'HIGH';
            if (confidence >= 0.6) return 'MEDIUM';
            return 'LOW';
        }

        function getCurrentSignals() {
            // Extract current signals from the scanner display
            const scannerResults = document.getElementById('scannerResults');
            const signalItems = scannerResults.querySelectorAll('.signal-item');

            const signals = [];
            signalItems.forEach(item => {
                const symbol = item.querySelector('.signal-symbol')?.textContent;
                const priceText = item.querySelector('.signal-price')?.textContent;
                const confidenceText = item.querySelector('.signal-confidence')?.textContent;

                if (symbol && symbol !== 'Loading signals...') {
                    const price = parseFloat(priceText?.replace('$', '') || '0');
                    const confidence = parseFloat(confidenceText?.match(/(\d+\.?\d*)%/)?.[1] || '0') / 100;

                    signals.push({
                        symbol,
                        price,
                        confidence,
                        pattern_type: 'Lee Method',
                        timestamp: new Date().toISOString()
                    });
                }
            });

            return signals;
        }

        function showNoSignalsMessage() {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">No active signals</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">Scanner is monitoring S&P 500...</div>
                        <div class="signal-strength strength-medium">SCANNING</div>
                    </div>
                </div>
            `;

            // Update active signals count to 0
            document.getElementById('activeSignals').textContent = '0';
        }

        function showErrorMessage(message) {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Scanner Error</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">${message}</div>
                        <div class="signal-strength strength-low">ERROR</div>
                    </div>
                </div>
            `;

            // Update active signals count to 0
            document.getElementById('activeSignals').textContent = '0';
        }

        function showErrorMessage(message) {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Connection Error</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">${message}</div>
                        <div class="signal-strength strength-low">ERROR</div>
                    </div>
                </div>
            `;
        }

        // SYSTEM-WIDE FIX: Frontend symbol validation
        function isValidStockSymbol(symbol) {
            // Must be string and 2-5 characters
            if (!symbol || typeof symbol !== 'string' || symbol.length < 2 || symbol.length > 5) {
                return false;
            }

            // Must be all uppercase letters
            if (!/^[A-Z]+$/.test(symbol)) {
                return false;
            }

            // Common patterns that are NOT stock symbols (ENHANCED)
            const nonSymbolPatterns = [
                /^[AEIOU]{2,}$/,  // All vowels
                /^(AM|PM|ET|PT|CT|MT|EST|PST|CST|MST)$/,  // Time zones (expanded)
                /^(US|USA|UK|EU|CA|JP|CN|FR|DE|IT|ES)$/,  // Country codes (expanded)
                /^(CEO|CFO|CTO|CMO|COO|VP|GM|HR|PR)$/,  // Executive titles (expanded)
                /^(API|URL|SQL|XML|JSON|CSV|PDF|HTTP|HTML|CSS)$/,  // Tech acronyms (expanded)
                /^(FBI|CIA|SEC|FDA|IRS|DOJ|EPA|FTC)$/,  // Government agencies
                /^(LLC|INC|CORP|LTD)$/,  // Business suffixes
            ];

            for (const pattern of nonSymbolPatterns) {
                if (pattern.test(symbol)) {
                    return false;
                }
            }

            return true;
        }

        async function showSignalDetails(symbol) {
            try {
                // SYSTEM-WIDE FIX: Validate symbol before making API call
                if (!isValidStockSymbol(symbol)) {
                    console.error(`❌ Invalid symbol format: ${symbol}`);
                    addBotMessage(`❌ Invalid symbol format: "${symbol}". Stock symbols must be 2-5 uppercase letters.`);
                    return;
                }

                console.log(`📊 Loading details for ${symbol}...`);

                const response = await fetch(`/api/v1/market_data/${symbol}`);
                const data = await response.json();

                if (data.success) {
                    // Add message to chat showing signal details
                    const message = `📈 **${symbol} Signal Analysis**\n\n` +
                        `**Current Price:** $${data.price}\n` +
                        `**Pattern:** Lee Method 5-Point TTM Squeeze\n` +
                        `**Confidence:** ${(data.confidence * 100).toFixed(1)}%\n` +
                        `**Entry Signal:** ${data.signal_type}\n\n` +
                        `**Technical Indicators:**\n` +
                        `• EMA 5/8 Trend: ${data.ema_trend || 'Calculating...'}\n` +
                        `• MACD Histogram: ${data.histogram_status || 'Analyzing...'}\n` +
                        `• TTM Squeeze: ${data.squeeze_status || 'Monitoring...'}\n\n` +
                        `Click refresh to get the latest analysis from A.T.L.A.S. AI.`;

                    addBotMessage(message);
                } else {
                    addBotMessage(`❌ Unable to load detailed analysis for ${symbol}. Please try again.`);
                }
            } catch (error) {
                console.error('❌ Error loading signal details:', error);
                addBotMessage(`❌ Error loading analysis for ${symbol}. Connection issue detected.`);
            }
        }

        // Chat functionality
        async function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            const message = chatInput.value.trim();

            if (!message) return;

            // STEP 1: Show intent detection bubble immediately
            const detectedIntent = detectUserIntent(message);
            intentBubble.show(detectedIntent.type, detectedIntent.message, detectedIntent.category);

            // Add user message to chat
            addUserMessage(message);

            // Clear input and disable send button
            chatInput.value = '';
            chatInput.style.height = 'auto';
            sendButton.disabled = true;

            try {
                console.log('💬 Sending message to A.T.L.A.S. AI...');

                // Add a test to bypass progress and show message immediately for debugging
                if (message.toLowerCase().includes('test')) {
                    console.log('🧪 Test mode detected - bypassing progress simulation');
                    setTimeout(() => {
                        removeLoadingMessage(loadingId);
                        addBotMessage('Test response: A.T.L.A.S. is working correctly!');
                    }, 500);
                    return;
                }

                // Send request to A.T.L.A.S. AI
                const response = await fetch('/api/v1/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId,
                        context: 'web_interface'
                    })
                });

                const data = await response.json();

                console.log('🔍 API Response Debug:', {
                    status: response.status,
                    ok: response.ok,
                    data: data,
                    hasResponse: !!data.response,
                    responseLength: data.response ? data.response.length : 0
                });

                if (response.ok && data.response) {
                    console.log('📝 About to display message:', data.response.substring(0, 100) + '...');

                    // Hide intent detection bubble and show response immediately
                    intentBubble.hide();
                    addBotMessage(data.response);
                    console.log('✅ A.T.L.A.S. AI response displayed successfully');
                } else {
                    // Show error state
                    console.error('❌ API Response Error:', {
                        status: response.status,
                        statusText: response.statusText,
                        data: data
                    });
                    intentBubble.hide(); // Hide intent bubble on error
                    addBotMessage('❌ Sorry, I encountered an error processing your request. Please try again.');
                    console.error('❌ Chat API error:', data);
                }

            } catch (error) {
                console.error('❌ Error sending message:', error);
                intentBubble.hide(); // Hide intent bubble on connection error
                addBotMessage('❌ Connection error. Please check your connection and try again.');
            } finally {
                sendButton.disabled = false;
                chatInput.focus();
            }
        }

        function addUserMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-user';
            messageDiv.textContent = message;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function addBotMessage(message) {
            console.log('🤖 addBotMessage called with:', {
                messageLength: message ? message.length : 0,
                messagePreview: message ? message.substring(0, 100) + '...' : 'null/undefined'
            });

            const chatMessages = document.getElementById('chatMessages');
            console.log('📦 Chat container found:', !!chatMessages);

            if (!chatMessages) {
                console.error('❌ Chat messages container not found!');
                return;
            }

            if (!message) {
                console.error('❌ No message provided to addBotMessage');
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-bot';

            // Convert markdown-style formatting to HTML
            const formattedMessage = message
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\n/g, '<br>');

            messageDiv.innerHTML = formattedMessage;
            console.log('📝 Message div created:', messageDiv.outerHTML.substring(0, 200) + '...');

            chatMessages.appendChild(messageDiv);
            console.log('✅ Message appended to chat, total messages:', chatMessages.children.length);

            scrollToBottom();
            console.log('📜 Scrolled to bottom');
        }

        function addLoadingMessage() {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const loadingId = 'loading_' + Date.now();

            messageDiv.id = loadingId;
            messageDiv.className = 'message message-loading';
            messageDiv.innerHTML = `
                A.T.L.A.S. AI is thinking...
                <div class="loading-dots">
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();

            return loadingId;
        }

        function updateProgressMessage(progressId, title, description) {
            const progressMessage = document.getElementById(progressId);
            if (progressMessage) {
                const titleElement = progressMessage.querySelector('.progress-title');
                const descElement = progressMessage.querySelector('.progress-status');

                if (titleElement) titleElement.textContent = title;
                if (descElement) descElement.textContent = description;

                // Update status based on title
                if (title.includes('✅') || title.includes('Complete')) {
                    progressMessage.dataset.status = 'completed';
                } else if (title.includes('❌') || title.includes('Error')) {
                    progressMessage.dataset.status = 'failed';
                } else {
                    progressMessage.dataset.status = 'processing';
                }

                console.log('📊 Progress updated:', title, '-', description);
            }
        }

        function removeLoadingMessage(loadingId) {
            console.log('🗑️ removeLoadingMessage called for:', loadingId);
            const loadingMessage = document.getElementById(loadingId);
            if (loadingMessage) {
                console.log('✅ Found loading message to remove:', loadingMessage.id);
                loadingMessage.dataset.status = 'completed';
                loadingMessage.style.transition = 'opacity 0.3s ease';
                loadingMessage.style.opacity = '0';
                setTimeout(() => {
                    if (loadingMessage.parentNode) {
                        loadingMessage.remove();
                        console.log('🗑️ Loading message removed successfully');
                    }
                }, 300);
            } else {
                console.warn('⚠️ Loading message not found:', loadingId);
            }
        }

        // Function to manually clear all progress messages (for debugging)
        function clearAllProgressMessages() {
            const progressMessages = document.querySelectorAll('.message-progress');
            progressMessages.forEach(msg => {
                console.log('🧹 Manually clearing progress message:', msg.id);
                msg.remove();
            });
            console.log(`✅ Cleared ${progressMessages.length} progress messages`);
        }

        // Make clearAllProgressMessages available globally for debugging
        window.clearAllProgressMessages = clearAllProgressMessages;

        // FUNCTIONAL PROGRESS TRACKING - Real backend progress updates
        function simulateProgressUpdates(progressId, message) {
            // Start with initial connection step
            setTimeout(() => {
                updateProgressMessage(progressId, "Connecting to A.T.L.A.S. AI... 🔌", "Establishing secure connection");
                updateProgressBar(progressId, 5);
            }, 100);

            // The rest will be handled by real backend progress updates
            // This is just a fallback for the initial connection phase
        }

        // NEW: Handle real progress updates from backend
        function handleRealProgressUpdate(progressData) {
            console.log('📊 Real progress update received:', progressData);

            const progressId = progressData.progress_id;
            const progressElement = document.getElementById(progressId);

            if (!progressElement) {
                console.warn('⚠️ Progress element not found for ID:', progressId);
                return;
            }

            // Update overall progress
            const overallProgress = Math.round(progressData.overall_progress * 100);
            updateProgressBar(progressId, overallProgress);

            // Update current step information
            const currentStep = progressData.steps.find(step => step.status === 'in_progress') ||
                               progressData.steps[progressData.steps.length - 1];

            if (currentStep) {
                const stepProgress = Math.round(currentStep.progress * 100);
                const title = `${currentStep.title} (${stepProgress}%)`;
                updateProgressMessage(progressId, title, currentStep.description);
            }

            // Update detailed steps display
            updateProgressSteps(progressId, progressData.steps);

            // Check if completed
            if (progressData.overall_progress >= 1.0) {
                setTimeout(() => {
                    updateProgressMessage(progressId, "Analysis Complete ✅ (100%)", "Ready to display results");
                }, 200);
            }
        }

        // NEW: Update progress bar
        function updateProgressBar(progressId, percentage) {
            const progressElement = document.getElementById(progressId);
            if (progressElement) {
                const progressBar = progressElement.querySelector('.progress-bar');
                if (progressBar) {
                    progressBar.style.width = `${percentage}%`;
                    progressBar.style.transition = 'width 0.3s ease-in-out';
                }

                const stepProgress = progressElement.querySelector('.step-progress');
                if (stepProgress) {
                    stepProgress.textContent = `${percentage}%`;
                }
            }
        }

        // NEW: Update detailed progress steps
        function updateProgressSteps(progressId, steps) {
            const progressElement = document.getElementById(progressId);
            if (!progressElement) return;

            const stepsContainer = progressElement.querySelector('.progress-steps');
            if (!stepsContainer) return;

            // Create step elements
            stepsContainer.innerHTML = steps.map(step => {
                const stepProgress = Math.round(step.progress * 100);
                const statusIcon = getStepStatusIcon(step.status);
                const statusClass = `step-${step.status}`;

                return `
                    <div class="progress-step ${statusClass}">
                        <div class="step-icon">${statusIcon}</div>
                        <div class="step-content">
                            <div class="step-name">${step.title}</div>
                            <div class="step-description">${step.description}</div>
                        </div>
                        <div class="step-progress">${stepProgress}%</div>
                    </div>
                `;
            }).join('');
        }

        // NEW: Get status icon for progress steps
        function getStepStatusIcon(status) {
            const icons = {
                'pending': '⏳',
                'in_progress': '🔄',
                'completed': '✅',
                'failed': '❌'
            };
            return icons[status] || '⏳';
        }

        // Enhanced progress tracking functions
        function addProgressMessage(title, description) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const progressId = 'progress_' + Date.now();

            messageDiv.id = progressId;
            messageDiv.className = 'message message-progress';
            messageDiv.dataset.timestamp = Date.now().toString(); // Add timestamp for cleanup
            messageDiv.dataset.status = 'processing'; // Track status for proper cleanup
            messageDiv.innerHTML = `
                <div class="progress-container">
                    <div class="progress-header">
                        <div class="progress-title">${title}</div>
                        <div class="progress-status status-in_progress">Starting...</div>
                    </div>
                    <div class="progress-description">${description}</div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: 0%"></div>
                    </div>
                    <div class="progress-steps">
                        <div class="progress-step step-pending">
                            <div class="step-icon">⏳</div>
                            <div class="step-content">
                                <div class="step-name">Initializing...</div>
                                <div class="step-description">Preparing to process your request</div>
                            </div>
                            <div class="step-progress">0%</div>
                        </div>
                    </div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();

            return progressId;
        }

        function updateOperationProgress(operation) {
            // Find existing progress message or create new one
            let progressElement = document.querySelector(`[data-operation-id="${operation.operation_id}"]`);

            if (!progressElement) {
                // Create new progress message
                const chatMessages = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message message-progress';
                messageDiv.setAttribute('data-operation-id', operation.operation_id);

                messageDiv.innerHTML = `
                    <div class="progress-container">
                        <div class="progress-header">
                            <div class="progress-title">${operation.title}</div>
                            <div class="progress-status">${getStatusText(operation.status)}</div>
                        </div>
                        <div class="progress-description">${operation.description}</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar" style="width: ${operation.overall_progress * 100}%"></div>
                        </div>
                        <div class="progress-steps"></div>
                    </div>
                `;

                chatMessages.appendChild(messageDiv);
                progressElement = messageDiv;
            }

            // Update progress bar
            const progressBar = progressElement.querySelector('.progress-bar');
            const progressStatus = progressElement.querySelector('.progress-status');
            const progressSteps = progressElement.querySelector('.progress-steps');

            if (progressBar) {
                progressBar.style.width = `${operation.overall_progress * 100}%`;
            }

            if (progressStatus) {
                progressStatus.textContent = getStatusText(operation.status);
                progressStatus.className = `progress-status status-${operation.status}`;
            }

            // Update steps with enhanced progress details
            if (progressSteps && operation.steps) {
                progressSteps.innerHTML = operation.steps.map(step => {
                    // Show detailed progress information for market scanning
                    let progressDetail = '';
                    if (step.progress_detail) {
                        progressDetail = `<div class="step-detail">${step.progress_detail}</div>`;
                    }

                    // Add data source indicators for market scanning steps
                    let dataSourceIcon = '';
                    if (step.name.includes('Fed')) {
                        dataSourceIcon = '🏛️';
                    } else if (step.name.includes('Reuters')) {
                        dataSourceIcon = '📰';
                    } else if (step.name.includes('Bloomberg')) {
                        dataSourceIcon = '📊';
                    } else if (step.name.includes('Intent')) {
                        dataSourceIcon = '🧠';
                    } else if (step.name.includes('Engine')) {
                        dataSourceIcon = '⚙️';
                    } else if (step.name.includes('Market Data')) {
                        dataSourceIcon = '📈';
                    }

                    return `
                        <div class="progress-step step-${step.status}">
                            <div class="step-icon">${dataSourceIcon || getStepIcon(step.status)}</div>
                            <div class="step-content">
                                <div class="step-name">${step.name}</div>
                                <div class="step-description">${step.description}</div>
                                ${progressDetail}
                            </div>
                            <div class="step-progress">${Math.round(step.progress * 100)}%</div>
                        </div>
                    `;
                }).join('');
            }

            // Auto-remove completed operations after 3 seconds
            if (operation.status === 'completed') {
                setTimeout(() => {
                    if (progressElement && progressElement.parentNode) {
                        progressElement.style.transition = 'opacity 0.5s ease';
                        progressElement.style.opacity = '0';
                        setTimeout(() => {
                            if (progressElement.parentNode) {
                                progressElement.remove();
                            }
                        }, 500);
                    }
                }, 3000);
            }

            scrollToBottom();
        }

        function getStatusText(status) {
            const statusMap = {
                'pending': 'Pending...',
                'in_progress': 'Processing...',
                'completed': 'Completed ✅',
                'failed': 'Failed ❌',
                'cancelled': 'Cancelled'
            };
            return statusMap[status] || status;
        }

        function getStepIcon(status) {
            const iconMap = {
                'pending': '⏳',
                'in_progress': '🔄',
                'completed': '✅',
                'failed': '❌',
                'cancelled': '⏹️'
            };
            return iconMap[status] || '⏳';
        }

        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Scanner control functions
        async function refreshScanner() {
            console.log('🔄 Refreshing scanner...');

            const refreshBtn = document.getElementById('refreshScanBtn');
            const originalText = refreshBtn.innerHTML;

            refreshBtn.innerHTML = '⏳ Refreshing...';
            refreshBtn.disabled = true;

            try {
                await loadLeeMethodSignals();
                await loadScannerStats();
                console.log('✅ Scanner refreshed successfully');
            } catch (error) {
                console.error('❌ Error refreshing scanner:', error);
            } finally {
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            }
        }

        function showScannerConfig() {
            addBotMessage(`⚙️ **Scanner Configuration**\n\n` +
                `**Current Settings:**\n` +
                `• Pattern: Lee Method 5-Point TTM Squeeze\n` +
                `• Market: S&P 500 (350+ symbols)\n` +
                `• Scan Interval: Real-time\n` +
                `• Confidence Threshold: 60%\n\n` +
                `**Pattern Criteria:**\n` +
                `1. 3+ declining MACD histogram bars\n` +
                `2. Histogram rebound (less negative)\n` +
                `3. EMA 5/8 uptrend confirmation\n` +
                `4. Optional TTM Squeeze filter\n` +
                `5. Target first less-negative bar\n\n` +
                `To modify settings, ask me: "Change scanner configuration"`);
        }

        function showLeeMethodCriteria() {
            addBotMessage(`📋 **Lee Method Pattern Criteria**\n\n` +
                `The Lee Method uses a sophisticated 5-point TTM Squeeze pattern detection algorithm:\n\n` +
                `**Point 1: Histogram Decline Pattern**\n` +
                `• Requires 3+ consecutive declining MACD histogram bars\n` +
                `• Each bar must be more negative than the previous\n` +
                `• Indicates building downward momentum\n\n` +
                `**Point 2: Histogram Rebound Signal**\n` +
                `• First bar that is less negative (rebounds)\n` +
                `• Signals potential momentum shift\n` +
                `• This is the primary entry trigger\n\n` +
                `**Point 3: EMA 5/8 Trend Confirmation**\n` +
                `• EMA 5 must be above EMA 8 (uptrend)\n` +
                `• Confirms bullish underlying trend\n` +
                `• Filters out false signals\n\n` +
                `**Point 4: TTM Squeeze State (Optional)**\n` +
                `• Bollinger Bands inside Keltner Channels\n` +
                `• Indicates low volatility compression\n` +
                `• Enhances signal reliability\n\n` +
                `**Point 5: Entry Timing**\n` +
                `• Target the first less-negative histogram bar\n` +
                `• Optimal entry for trend reversal\n` +
                `• Risk management with stop-loss below recent low\n\n` +
                `This pattern has shown **87% accuracy** in backtesting on S&P 500 stocks.`);
        }

        function setupAutoRefresh() {
            // Refresh scanner data every 30 seconds
            setInterval(async () => {
                if (isConnected) {
                    try {
                        await loadLeeMethodSignals();
                        await loadScannerStats();
                    } catch (error) {
                        console.error('❌ Auto-refresh error:', error);
                    }
                }
            }, 30000);

            // Test API connectivity every 60 seconds
            setInterval(async () => {
                await testApiConnectivity();
            }, 60000);
        }

        // Utility functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(price);
        }

        // Error handling
        window.addEventListener('error', function(event) {
            console.error('❌ Global error:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('❌ Unhandled promise rejection:', event.reason);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (websocket) {
                websocket.close();
            }
        });

        // System Status Dashboard Functions
        async function showSystemStatus() {
            console.log('📊 Loading system status dashboard...');

            const statusPanel = document.getElementById('systemStatusPanel');
            statusPanel.style.display = 'block';

            // Load system health data
            try {
                const response = await fetch('/api/v1/system/health');
                const healthData = await response.json();

                // Update status indicators
                updateStatusIndicator('marketHoursStatus',
                    healthData.market_hours || 'Unknown',
                    healthData.market_hours ? 'healthy' : 'warning');

                updateStatusIndicator('scannerStatus',
                    healthData.scanner_status?.status || 'Unknown',
                    healthData.scanner_status?.status === 'running' ? 'healthy' : 'warning');

                updateStatusIndicator('aiSystemsStatus',
                    healthData.orchestrator_status?.status || 'Unknown',
                    healthData.orchestrator_status?.status === 'running' ? 'healthy' : 'error');

                updateStatusIndicator('dataSourcesStatus', 'Active', 'healthy');

                const convHealth = healthData.conversation_monitoring || {};
                updateStatusIndicator('responseTimeStatus',
                    `${convHealth.average_response_time || 0}s`,
                    convHealth.average_response_time < 10 ? 'healthy' : 'warning');

                updateStatusIndicator('successRateStatus',
                    `${convHealth.success_rate || 0}%`,
                    convHealth.success_rate > 90 ? 'healthy' : 'warning');

                // Load recent alerts
                await loadRecentAlerts();

            } catch (error) {
                console.error('Failed to load system status:', error);
                updateStatusIndicator('scannerStatus', 'Error', 'error');
            }
        }

        function hideSystemStatus() {
            const statusPanel = document.getElementById('systemStatusPanel');
            statusPanel.style.display = 'none';
        }

        function updateStatusIndicator(elementId, value, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
                element.className = `status-value status-${status}`;
            }
        }

        async function loadRecentAlerts() {
            try {
                const response = await fetch('/api/v1/monitoring/alerts?hours=1');
                const alertsData = await response.json();

                const alertsList = document.getElementById('alertsList');

                if (alertsData.alerts && alertsData.alerts.length > 0) {
                    alertsList.innerHTML = alertsData.alerts.slice(0, 5).map(alert => `
                        <div class="alert-item alert-level-${alert.alert_level}">
                            <strong>${alert.alert_level.toUpperCase()}:</strong> ${alert.description}
                            <div style="font-size: 10px; color: #8892b0; margin-top: 2px;">
                                ${new Date(alert.timestamp).toLocaleTimeString()}
                            </div>
                        </div>
                    `).join('');
                } else {
                    alertsList.innerHTML = '<div style="color: #8892b0; font-style: italic;">No recent alerts</div>';
                }

            } catch (error) {
                console.error('Failed to load alerts:', error);
                document.getElementById('alertsList').innerHTML = '<div style="color: #ff4444;">Failed to load alerts</div>';
            }
        }

        // LIVE TRADING SAFETY: Data status monitoring functions
        function initializeDataStatusMonitoring() {
            console.log('📊 Initializing data status monitoring...');

            // Start with checking status
            updateDataStatus('checking', 'Checking Data Sources...');

            // Check data status every 30 seconds
            setInterval(checkDataSourceHealth, 30000);

            // Initial check after 2 seconds
            setTimeout(checkDataSourceHealth, 2000);
        }

        async function checkDataSourceHealth() {
            try {
                const response = await fetch('/api/data-status');
                const status = await response.json();

                if (status.trading_halted) {
                    updateDataStatus('critical', `🚨 TRADING HALTED: ${status.halt_reason || 'Data Issues'}`);
                    showDataUnavailableAlert(status.halt_reason);
                } else if (status.critical_failure_count > 0) {
                    updateDataStatus('warning', `⚠️ Data Issues (${status.data_failure_count} failures)`);
                } else if (status.system_status === 'OPERATIONAL') {
                    updateDataStatus('healthy', '✅ All Data Sources Online');
                } else {
                    updateDataStatus('warning', '❓ Data Status Unknown');
                }

            } catch (error) {
                console.error('Failed to check data status:', error);
                updateDataStatus('warning', '⚠️ Unable to Check Data Status');
            }
        }

        function updateDataStatus(status, message) {
            const dot = document.getElementById('dataStatusDot');
            const text = document.getElementById('dataStatusText');
            const bar = document.getElementById('dataStatusBar');

            if (!dot || !text || !bar) return;

            // Remove existing status classes
            dot.classList.remove('healthy', 'warning', 'critical');

            // Add new status class
            dot.classList.add(status);

            // Update text
            text.textContent = message;

            // Update bar color
            switch (status) {
                case 'healthy':
                    bar.style.borderLeftColor = '#4CAF50';
                    break;
                case 'warning':
                    bar.style.borderLeftColor = '#ff9800';
                    break;
                case 'critical':
                    bar.style.borderLeftColor = '#f44336';
                    break;
                default:
                    bar.style.borderLeftColor = '#ffa500';
            }

            console.log(`📊 Data status updated: ${status} - ${message}`);
        }

        function showDataUnavailableAlert(reason) {
            const alertMessage = `🚨 CRITICAL ALERT: TRADING OPERATIONS SUSPENDED 🚨

Reason: ${reason || 'Market data unavailable'}

For your safety, all trading recommendations and analysis have been suspended until data integrity is restored.

⚠️ DO NOT MAKE TRADING DECISIONS based on potentially outdated information.

Manual intervention is required to resume operations.`;

            // Add critical alert message to chat
            addBotMessage(alertMessage);
        }

        // Initialize data monitoring when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initializeDataStatusMonitoring, 1000);
        });

        // INTENT DETECTION BUBBLE SYSTEM - ChatGPT Style
        class IntentDetectionBubble {
            constructor() {
                this.bubble = document.getElementById('intentDetectionBubble');
                this.icon = document.getElementById('intentBubbleIcon');
                this.text = document.getElementById('intentBubbleText');
                this.category = document.getElementById('intentBubbleCategory');
                this.isVisible = false;
                this.hideTimeout = null;
            }

            show(intentType, message, category = null) {
                if (!this.bubble) return;

                // Clear any existing hide timeout
                if (this.hideTimeout) {
                    clearTimeout(this.hideTimeout);
                    this.hideTimeout = null;
                }

                // Set content based on intent type
                const intentConfig = this.getIntentConfig(intentType);

                this.icon.textContent = intentConfig.icon;
                this.text.textContent = message || intentConfig.defaultMessage;
                this.category.textContent = category || intentConfig.category;

                // Apply intent-specific styling
                this.bubble.className = `intent-detection-bubble show ${intentConfig.className}`;

                this.isVisible = true;
                console.log(`🧠 Intent bubble shown: ${intentType} - ${message}`);
            }

            hide(delay = 0) {
                if (!this.bubble || !this.isVisible) return;

                if (delay > 0) {
                    this.hideTimeout = setTimeout(() => {
                        this._hideImmediate();
                    }, delay);
                } else {
                    this._hideImmediate();
                }
            }

            _hideImmediate() {
                if (this.bubble) {
                    this.bubble.classList.remove('show');
                    this.isVisible = false;
                    console.log('🧠 Intent bubble hidden');
                }
            }

            update(message, category = null) {
                if (!this.isVisible) return;

                if (message) this.text.textContent = message;
                if (category) this.category.textContent = category;
            }

            getIntentConfig(intentType) {
                const configs = {
                    'stock_analysis': {
                        icon: '📊',
                        defaultMessage: 'Analyzing stock request...',
                        category: 'Stock Analysis',
                        className: 'intent-bubble-stock-analysis'
                    },
                    'live_trading': {
                        icon: '⚡',
                        defaultMessage: 'Processing trading request...',
                        category: 'Live Trading',
                        className: 'intent-bubble-trading'
                    },
                    'options_strategy': {
                        icon: '🎯',
                        defaultMessage: 'Analyzing options strategy...',
                        category: 'Options',
                        className: 'intent-bubble-options'
                    },
                    'lee_method': {
                        icon: '🔍',
                        defaultMessage: 'Scanning for patterns...',
                        category: 'Lee Method',
                        className: 'intent-bubble-stock-analysis'
                    },
                    'ml_prediction': {
                        icon: '🤖',
                        defaultMessage: 'Running ML predictions...',
                        category: 'AI Prediction',
                        className: 'intent-bubble-prediction'
                    },
                    'risk_assessment': {
                        icon: '🛡️',
                        defaultMessage: 'Calculating risk metrics...',
                        category: 'Risk Analysis',
                        className: 'intent-bubble-risk'
                    },
                    'sentiment_analysis': {
                        icon: '💭',
                        defaultMessage: 'Analyzing market sentiment...',
                        category: 'Sentiment',
                        className: 'intent-bubble-news'
                    },
                    'news_insights': {
                        icon: '📰',
                        defaultMessage: 'Gathering news insights...',
                        category: 'News Analysis',
                        className: 'intent-bubble-news'
                    },
                    'education': {
                        icon: '📚',
                        defaultMessage: 'Preparing educational content...',
                        category: 'Education',
                        className: 'intent-bubble-education'
                    },
                    'profit_focused': {
                        icon: '💰',
                        defaultMessage: 'Finding profit opportunities...',
                        category: 'Profit Focus',
                        className: 'intent-bubble-trading'
                    },
                    'greeting': {
                        icon: '👋',
                        defaultMessage: 'Processing greeting...',
                        category: 'Welcome',
                        className: 'intent-bubble-education'
                    },
                    'general': {
                        icon: '🧠',
                        defaultMessage: 'Understanding your request...',
                        category: 'General',
                        className: ''
                    }
                };

                return configs[intentType] || configs['general'];
            }
        }

        // Global intent bubble instance
        const intentBubble = new IntentDetectionBubble();

        // CLIENT-SIDE INTENT DETECTION - Fast local analysis with enhanced stock symbol detection
        function detectUserIntent(message) {
            const messageLower = message.toLowerCase();

            // ENHANCED: Extract stock symbols (2-5 capital letters, common patterns)
            const symbolPattern = /\b[A-Z]{2,5}\b/g;
            const symbols = message.match(symbolPattern) || [];

            // Filter out common false positives for stock symbols
            const commonWords = new Set(['THE', 'AND', 'OR', 'BUT', 'FOR', 'AT', 'TO', 'FROM', 'WITH', 'BY', 'ALL',
                'YOU', 'CAN', 'GET', 'SET', 'PUT', 'NEW', 'OLD', 'BIG', 'BAD', 'GOOD', 'BEST',
                'API', 'URL', 'HTTP', 'HTML', 'CSS', 'SQL', 'PDF', 'XML', 'JSON', 'CSV',
                'USA', 'USD', 'CEO', 'CFO', 'CTO', 'IPO', 'SEC', 'FDA', 'AI', 'ML', 'IT',
                'AM', 'PM', 'ET', 'PT', 'CT', 'MT', 'EST', 'PST', 'CST', 'MST']);

            // Known stock symbols for better accuracy
            const knownStocks = new Set(['AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
                'ADBE', 'CRM', 'ORCL', 'INTC', 'AMD', 'QCOM', 'AVGO', 'TXN', 'CSCO', 'IBM', 'UBER',
                'SPY', 'QQQ', 'IWM', 'VTI', 'VOO', 'GLD', 'SLV', 'BTC', 'ETH']);

            // Filter symbols: prioritize known stocks, exclude common words
            const validSymbols = symbols.filter(symbol =>
                knownStocks.has(symbol) || (!commonWords.has(symbol) && symbol.length >= 2)
            );

            // SPECIAL CASE: Message contains only stock symbols (like "AAPL NVDA NFLX")
            const messageWords = message.trim().split(/\s+/);
            const isOnlySymbols = messageWords.length > 0 &&
                messageWords.every(word => /^[A-Z]{2,5}$/.test(word) &&
                (knownStocks.has(word) || !commonWords.has(word)));

            if (isOnlySymbols && validSymbols.length > 0) {
                return {
                    type: 'stock_analysis',
                    message: `Analyzing ${validSymbols.join(', ')}...`,
                    category: 'Stock Analysis'
                };
            }

            // Greeting detection
            if (/\b(hello|hi|hey|start|begin)\b/.test(messageLower)) {
                return {
                    type: 'greeting',
                    message: 'Processing greeting...',
                    category: 'Welcome'
                };
            }

            // Profit-focused detection
            if (/\b(make money|profit|profitable|best trades|opportunities|scan market|find trades)\b/.test(messageLower)) {
                return {
                    type: 'profit_focused',
                    message: 'Finding profit opportunities...',
                    category: 'Profit Focus'
                };
            }

            // Options strategy detection
            if (/\b(options? strategy|options? analysis|call option|put option|greeks|delta|gamma|theta|vega|implied volatility|straddle|strangle)\b/.test(messageLower)) {
                const symbolText = validSymbols.length > 0 ? ` for ${validSymbols.join(', ')}` : '';
                return {
                    type: 'options_strategy',
                    message: `Analyzing options strategy${symbolText}...`,
                    category: 'Options'
                };
            }

            // Live trading detection
            if (/\b(buy|sell|trade|execute|place order|live trading)\b/.test(messageLower)) {
                const symbolText = validSymbols.length > 0 ? ` for ${validSymbols.join(', ')}` : '';
                return {
                    type: 'live_trading',
                    message: `Processing trading request${symbolText}...`,
                    category: 'Live Trading'
                };
            }

            // Lee Method detection
            if (/\b(lee method|pattern|scan|momentum|histogram|ttm squeeze|squeeze pattern|bollinger|keltner)\b/.test(messageLower)) {
                const symbolText = validSymbols.length > 0 ? ` for ${validSymbols.join(', ')}` : '';
                return {
                    type: 'lee_method',
                    message: `Scanning for patterns${symbolText}...`,
                    category: 'Lee Method'
                };
            }

            // ML prediction detection
            if (/\b(predict|forecast|ml|lstm|neural network|price prediction)\b/.test(messageLower)) {
                const symbolText = validSymbols.length > 0 ? ` for ${validSymbols.join(', ')}` : '';
                return {
                    type: 'ml_prediction',
                    message: `Running ML predictions${symbolText}...`,
                    category: 'AI Prediction'
                };
            }

            // Risk assessment detection
            if (/\b(risk|var|position size|portfolio risk|risk management)\b/.test(messageLower)) {
                const symbolText = validSymbols.length > 0 ? ` for ${validSymbols.join(', ')}` : '';
                return {
                    type: 'risk_assessment',
                    message: `Calculating risk metrics${symbolText}...`,
                    category: 'Risk Analysis'
                };
            }

            // News insights detection
            if (/\b(market news|latest news|breaking news|news today|news sentiment|news impact|market moving news)\b/.test(messageLower)) {
                return {
                    type: 'news_insights',
                    message: 'Gathering news insights...',
                    category: 'News Analysis'
                };
            }

            // Sentiment analysis detection
            if (/\b(sentiment|market sentiment)\b/.test(messageLower)) {
                return {
                    type: 'sentiment_analysis',
                    message: 'Analyzing market sentiment...',
                    category: 'Sentiment'
                };
            }

            // Stock analysis detection (includes price queries) - ENHANCED with symbol validation
            if (/\b(analyze|price|current price|quote|trading at|stock price|volume|change|percentage|market data|chart|6 point|six point|trading analysis)\b/.test(messageLower) || validSymbols.length > 0) {
                const symbolText = validSymbols.length > 0 ? ` for ${validSymbols.join(', ')}` : '';
                return {
                    type: 'stock_analysis',
                    message: `Analyzing stock request${symbolText}...`,
                    category: 'Stock Analysis'
                };
            }

            // Education detection
            if (/\b(what is|how to|explain|learn|education|beginner|tutorial|guide)\b/.test(messageLower)) {
                return {
                    type: 'education',
                    message: 'Preparing educational content...',
                    category: 'Education'
                };
            }

            // Default general intent
            return {
                type: 'general',
                message: 'Understanding your request...',
                category: 'General'
            };
        }

        console.log('🚀 A.T.L.A.S. Interface JavaScript loaded successfully');
    </script>
</body>
</html>
