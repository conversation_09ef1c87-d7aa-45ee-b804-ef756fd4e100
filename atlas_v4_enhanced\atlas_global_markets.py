"""
A.T.L.A.S. Global Market Coverage Engine
International markets, cryptocurrency, forex, and commodities integration
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import json

# Core imports
from models import EngineStatus

# Global market data imports (with graceful fallbacks)
try:
    import yfinance as yf
    import ccxt
    import requests
    GLOBAL_MARKET_LIBS_AVAILABLE = True
except ImportError:
    GLOBAL_MARKET_LIBS_AVAILABLE = False

# Grok integration (with graceful fallback)
try:
    from atlas_grok_integration import AtlasGrokIntegrationEngine, GrokTaskType, GrokCapability
    GROK_INTEGRATION_AVAILABLE = True
except ImportError:
    GROK_INTEGRATION_AVAILABLE = False

logger = logging.getLogger(__name__)

# ============================================================================
# GLOBAL MARKET MODELS
# ============================================================================

class MarketType(Enum):
    """Types of global markets"""
    EQUITY = "equity"
    CRYPTOCURRENCY = "cryptocurrency"
    FOREX = "forex"
    COMMODITIES = "commodities"
    BONDS = "bonds"
    DERIVATIVES = "derivatives"
    INDICES = "indices"

class MarketRegion(Enum):
    """Global market regions"""
    NORTH_AMERICA = "north_america"
    EUROPE = "europe"
    ASIA_PACIFIC = "asia_pacific"
    LATIN_AMERICA = "latin_america"
    MIDDLE_EAST = "middle_east"
    AFRICA = "africa"
    GLOBAL = "global"

class TradingSession(Enum):
    """Global trading sessions"""
    SYDNEY = "sydney"
    TOKYO = "tokyo"
    LONDON = "london"
    NEW_YORK = "new_york"
    AFTER_HOURS = "after_hours"
    PRE_MARKET = "pre_market"

@dataclass
class GlobalMarketData:
    """Global market data point"""
    symbol: str
    market_type: MarketType
    region: MarketRegion
    price: float
    volume: float
    change_24h: float
    change_percent_24h: float
    market_cap: Optional[float]
    trading_session: TradingSession
    currency: str
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class CrossMarketCorrelation:
    """Cross-market correlation analysis"""
    market_pair: Tuple[str, str]
    correlation_coefficient: float
    significance: float
    time_period: str
    market_types: Tuple[MarketType, MarketType]
    regions: Tuple[MarketRegion, MarketRegion]
    analysis_timestamp: datetime

@dataclass
class GlobalMarketSentiment:
    """Global market sentiment analysis"""
    region: MarketRegion
    overall_sentiment: float  # -1.0 to 1.0
    fear_greed_index: float  # 0.0 to 1.0
    volatility_index: float
    risk_on_off: str  # 'risk_on', 'risk_off', 'neutral'
    dominant_themes: List[str]
    market_drivers: List[str]
    timestamp: datetime

@dataclass
class ArbitrageOpportunity:
    """Cross-market arbitrage opportunity"""
    opportunity_id: str
    asset: str
    market_1: Dict[str, Any]
    market_2: Dict[str, Any]
    price_difference: float
    profit_potential: float
    execution_complexity: str  # 'low', 'medium', 'high'
    time_sensitivity: int  # minutes
    risk_factors: List[str]
    timestamp: datetime

# ============================================================================
# GLOBAL MARKETS ENGINE
# ============================================================================

class AtlasGlobalMarketsEngine:
    """Global market coverage and analysis engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.libs_available = GLOBAL_MARKET_LIBS_AVAILABLE
        self.grok_integration_available = GROK_INTEGRATION_AVAILABLE

        # Market data sources
        self.equity_sources = {}
        self.crypto_exchanges = {}
        self.forex_providers = {}
        self.commodity_sources = {}

        # Market sessions and timezones
        self.trading_sessions = {
            TradingSession.SYDNEY: {'start': '21:00', 'end': '06:00', 'timezone': 'Australia/Sydney'},
            TradingSession.TOKYO: {'start': '00:00', 'end': '09:00', 'timezone': 'Asia/Tokyo'},
            TradingSession.LONDON: {'start': '08:00', 'end': '16:30', 'timezone': 'Europe/London'},
            TradingSession.NEW_YORK: {'start': '09:30', 'end': '16:00', 'timezone': 'America/New_York'}
        }

        # Grok integration for real-time sentiment and news analysis
        self.grok_engine = None
        self.enhanced_analyses = {}
        self.sentiment_cache = {}
        
        # Global market data cache
        self.market_data_cache = {}
        self.correlation_cache = {}
        self.sentiment_cache = {}
        
        # Configuration
        self.supported_regions = list(MarketRegion)
        self.supported_markets = list(MarketType)
        self.cache_ttl = 300  # 5 minutes
        
        logger.info(f"[GLOBAL] Global Markets Engine initialized - libs: {self.libs_available}")

    async def initialize(self):
        """Initialize global markets engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            if self.libs_available:
                await self._initialize_market_sources()
                await self._initialize_crypto_exchanges()
                await self._initialize_forex_providers()
                logger.info("[OK] Global market sources initialized")
            else:
                logger.warning("[FALLBACK] Global market libraries not available")
            
            # Initialize market sessions tracking
            await self._initialize_session_tracking()

            # Initialize Grok integration for real-time sentiment and news
            if self.grok_integration_available:
                try:
                    self.grok_engine = AtlasGrokIntegrationEngine()
                    grok_success = await self.grok_engine.initialize()
                    if grok_success:
                        logger.info("[OK] Grok integration initialized for global markets")
                    else:
                        logger.warning("[FALLBACK] Grok API not available - enhanced sentiment analysis disabled")
                except Exception as e:
                    logger.error(f"Grok integration initialization failed: {e}")
                    self.grok_engine = None
            else:
                logger.warning("[FALLBACK] Grok integration not available")

            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Global Markets Engine fully initialized")
            
        except Exception as e:
            logger.error(f"Global markets engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def get_enhanced_market_sentiment(self, symbol: str, market_type: MarketType,
                                          region: MarketRegion = MarketRegion.NORTH_AMERICA) -> Dict[str, Any]:
        """Get enhanced market sentiment using Grok real-time analysis"""
        try:
            # Get base market data
            base_data = await self.get_market_data(symbol, market_type, region)

            if not self.grok_engine:
                return {
                    'symbol': symbol,
                    'market_type': market_type.value,
                    'region': region.value,
                    'base_data': base_data,
                    'enhanced_sentiment': None,
                    'grok_enhanced': False,
                    'error': 'Grok integration not available'
                }

            # Check sentiment cache
            cache_key = f"{symbol}_{market_type.value}_{region.value}"
            if cache_key in self.sentiment_cache:
                cached_data = self.sentiment_cache[cache_key]
                if (datetime.now() - cached_data['timestamp']).seconds < 300:  # 5 min cache
                    return cached_data['result']

            # Create context for Grok sentiment analysis
            market_context = {
                'symbol': symbol,
                'market_type': market_type.value,
                'region': region.value,
                'current_price': base_data.get('current_price', 0),
                'volume': base_data.get('volume', 0),
                'change_percent': base_data.get('change_percent', 0)
            }

            # Prepare text data for sentiment analysis
            text_data = f"Market analysis for {symbol} in {region.value} {market_type.value} market. "
            text_data += f"Current price: {market_context['current_price']}, "
            text_data += f"Volume: {market_context['volume']}, "
            text_data += f"Change: {market_context['change_percent']}%"

            # Get enhanced sentiment from Grok
            enhanced_result = await self.grok_engine.sentiment_enhancer.enhance_sentiment_analysis(
                {'base_sentiment': 'neutral', 'confidence': 0.5}, text_data, symbol
            )

            # Store enhanced analysis
            self.enhanced_analyses[cache_key] = enhanced_result

            result = {
                'symbol': symbol,
                'market_type': market_type.value,
                'region': region.value,
                'base_data': base_data,
                'enhanced_sentiment': {
                    'sentiment_analysis': enhanced_result.grok_enhancement.content if enhanced_result.grok_enhancement.success else None,
                    'confidence': enhanced_result.combined_confidence,
                    'reasoning_chain': enhanced_result.reasoning_chain,
                    'improvement_metrics': enhanced_result.improvement_metrics
                },
                'grok_enhanced': True,
                'timestamp': datetime.now().isoformat()
            }

            # Cache result
            self.sentiment_cache[cache_key] = {
                'result': result,
                'timestamp': datetime.now()
            }

            logger.info(f"[GROK] Enhanced sentiment analysis for {symbol} in {region.value}")
            return result

        except Exception as e:
            logger.error(f"Enhanced market sentiment failed for {symbol}: {e}")
            return {
                'symbol': symbol,
                'market_type': market_type.value,
                'region': region.value,
                'error': str(e),
                'grok_enhanced': False
            }

    async def get_real_time_market_news(self, symbols: List[str],
                                      market_types: List[MarketType] = None) -> Dict[str, Any]:
        """Get real-time market news and sentiment using Grok's search capabilities"""
        try:
            if not self.grok_engine:
                return {
                    'symbols': symbols,
                    'news_analysis': None,
                    'grok_enhanced': False,
                    'error': 'Grok integration not available'
                }

            if not market_types:
                market_types = [MarketType.EQUITY]

            # Prepare search query for real-time news
            search_query = f"Latest market news and analysis for {', '.join(symbols)} "
            search_query += f"in {', '.join([mt.value for mt in market_types])} markets. "
            search_query += "Include price movements, volume analysis, and market sentiment."

            # Use Grok's real-time search capability
            from atlas_grok_integration import GrokRequest, GrokTaskType, GrokCapability

            grok_request = GrokRequest(
                task_type=GrokTaskType.REAL_TIME_SENTIMENT,
                capability=GrokCapability.REAL_TIME_SEARCH,
                prompt=search_query,
                temperature=0.3,
                context={
                    'symbols': symbols,
                    'market_types': [mt.value for mt in market_types],
                    'search_type': 'real_time_news'
                }
            )

            grok_response = await self.grok_engine.grok_client.make_request(grok_request)

            if grok_response.success:
                result = {
                    'symbols': symbols,
                    'market_types': [mt.value for mt in market_types],
                    'news_analysis': grok_response.content,
                    'confidence': grok_response.confidence,
                    'processing_time': grok_response.processing_time,
                    'tokens_used': grok_response.tokens_used,
                    'grok_enhanced': True,
                    'timestamp': datetime.now().isoformat()
                }

                logger.info(f"[GROK] Real-time news analysis completed for {len(symbols)} symbols")
                return result
            else:
                logger.error(f"Grok real-time news search failed: {grok_response.error_message}")
                return {
                    'symbols': symbols,
                    'news_analysis': None,
                    'error': grok_response.error_message,
                    'grok_enhanced': False
                }

        except Exception as e:
            logger.error(f"Real-time market news failed: {e}")
            return {
                'symbols': symbols,
                'error': str(e),
                'grok_enhanced': False
            }

    async def _initialize_market_sources(self):
        """Initialize equity market data sources"""
        try:
            # Major global equity markets
            self.equity_sources = {
                MarketRegion.NORTH_AMERICA: {
                    'exchanges': ['NYSE', 'NASDAQ', 'TSX'],
                    'indices': ['^GSPC', '^DJI', '^IXIC', '^GSPTSE'],
                    'major_stocks': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
                },
                MarketRegion.EUROPE: {
                    'exchanges': ['LSE', 'Euronext', 'XETRA'],
                    'indices': ['^FTSE', '^FCHI', '^GDAXI', '^STOXX50E'],
                    'major_stocks': ['ASML.AS', 'SAP', 'LVMH.PA', 'NESN.SW']
                },
                MarketRegion.ASIA_PACIFIC: {
                    'exchanges': ['TSE', 'HKEX', 'ASX', 'BSE'],
                    'indices': ['^N225', '^HSI', '^AXJO', '^BSESN'],
                    'major_stocks': ['7203.T', '0700.HK', 'CBA.AX', 'RELIANCE.BO']
                }
            }
            
            logger.info("[EQUITY] Equity market sources initialized")
            
        except Exception as e:
            logger.error(f"Equity market source initialization failed: {e}")
            self.libs_available = False

    async def _initialize_crypto_exchanges(self):
        """Initialize cryptocurrency exchange connections"""
        try:
            if self.libs_available and 'ccxt' in globals():
                # Major cryptocurrency exchanges
                self.crypto_exchanges = {
                    'binance': {'region': MarketRegion.GLOBAL, 'type': 'spot'},
                    'coinbase': {'region': MarketRegion.NORTH_AMERICA, 'type': 'spot'},
                    'kraken': {'region': MarketRegion.EUROPE, 'type': 'spot'},
                    'bitflyer': {'region': MarketRegion.ASIA_PACIFIC, 'type': 'spot'}
                }
                
                # Major cryptocurrencies to track
                self.crypto_symbols = [
                    'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 
                    'SOL/USDT', 'XRP/USDT', 'DOT/USDT', 'AVAX/USDT'
                ]
            
            logger.info("[CRYPTO] Cryptocurrency exchanges initialized")
            
        except Exception as e:
            logger.error(f"Crypto exchange initialization failed: {e}")

    async def _initialize_forex_providers(self):
        """Initialize forex data providers"""
        try:
            # Major currency pairs
            self.forex_providers = {
                'major_pairs': [
                    'EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF',
                    'AUD/USD', 'USD/CAD', 'NZD/USD'
                ],
                'minor_pairs': [
                    'EUR/GBP', 'EUR/JPY', 'GBP/JPY', 'CHF/JPY',
                    'EUR/CHF', 'AUD/JPY', 'GBP/CHF'
                ],
                'exotic_pairs': [
                    'USD/SGD', 'USD/HKD', 'EUR/TRY', 'USD/ZAR'
                ]
            }
            
            logger.info("[FOREX] Forex providers initialized")
            
        except Exception as e:
            logger.error(f"Forex provider initialization failed: {e}")

    async def _initialize_session_tracking(self):
        """Initialize trading session tracking"""
        try:
            # Current active sessions
            self.active_sessions = []
            
            # Update active sessions
            await self._update_active_sessions()
            
            logger.info("[SESSIONS] Trading session tracking initialized")
            
        except Exception as e:
            logger.error(f"Session tracking initialization failed: {e}")

    async def get_global_market_data(self, symbols: List[str], 
                                   market_type: MarketType,
                                   region: Optional[MarketRegion] = None) -> List[GlobalMarketData]:
        """Get global market data for specified symbols"""
        try:
            market_data = []
            
            for symbol in symbols:
                # Check cache first
                cache_key = f"{symbol}_{market_type.value}_{region.value if region else 'all'}"
                if cache_key in self.market_data_cache:
                    cached_data = self.market_data_cache[cache_key]
                    if (datetime.now() - cached_data['timestamp']).seconds < self.cache_ttl:
                        market_data.append(cached_data['data'])
                        continue
                
                # Fetch fresh data
                if market_type == MarketType.EQUITY:
                    data = await self._fetch_equity_data(symbol, region)
                elif market_type == MarketType.CRYPTOCURRENCY:
                    data = await self._fetch_crypto_data(symbol)
                elif market_type == MarketType.FOREX:
                    data = await self._fetch_forex_data(symbol)
                elif market_type == MarketType.COMMODITIES:
                    data = await self._fetch_commodity_data(symbol)
                else:
                    data = await self._fetch_generic_data(symbol, market_type, region)
                
                if data:
                    market_data.append(data)
                    # Cache the data
                    self.market_data_cache[cache_key] = {
                        'data': data,
                        'timestamp': datetime.now()
                    }
            
            return market_data
            
        except Exception as e:
            logger.error(f"Global market data retrieval failed: {e}")
            return []

    async def _fetch_equity_data(self, symbol: str, region: Optional[MarketRegion]) -> Optional[GlobalMarketData]:
        """Fetch equity market data"""
        try:
            if not self.libs_available:
                return self._create_mock_equity_data(symbol, region)
            
            # Use yfinance to fetch data (if available)
            # In production, would use multiple data sources
            return self._create_mock_equity_data(symbol, region)
            
        except Exception as e:
            logger.error(f"Equity data fetch failed for {symbol}: {e}")
            return None

    async def _fetch_crypto_data(self, symbol: str) -> Optional[GlobalMarketData]:
        """Fetch cryptocurrency data"""
        try:
            if not self.libs_available:
                return self._create_mock_crypto_data(symbol)
            
            # Use ccxt to fetch from exchanges (if available)
            return self._create_mock_crypto_data(symbol)
            
        except Exception as e:
            logger.error(f"Crypto data fetch failed for {symbol}: {e}")
            return None

    async def _fetch_forex_data(self, symbol: str) -> Optional[GlobalMarketData]:
        """Fetch forex data"""
        try:
            return self._create_mock_forex_data(symbol)
            
        except Exception as e:
            logger.error(f"Forex data fetch failed for {symbol}: {e}")
            return None

    async def _fetch_commodity_data(self, symbol: str) -> Optional[GlobalMarketData]:
        """Fetch commodity data"""
        try:
            return self._create_mock_commodity_data(symbol)
            
        except Exception as e:
            logger.error(f"Commodity data fetch failed for {symbol}: {e}")
            return None

    async def _fetch_generic_data(self, symbol: str, market_type: MarketType, 
                                region: Optional[MarketRegion]) -> Optional[GlobalMarketData]:
        """Fetch generic market data"""
        try:
            # Mock data for unsupported market types
            return GlobalMarketData(
                symbol=symbol,
                market_type=market_type,
                region=region or MarketRegion.GLOBAL,
                price=100.0 + np.random.uniform(-10, 10),
                volume=1000000 + np.random.uniform(-100000, 100000),
                change_24h=np.random.uniform(-5, 5),
                change_percent_24h=np.random.uniform(-0.05, 0.05),
                market_cap=None,
                trading_session=TradingSession.GLOBAL,
                currency='USD',
                timestamp=datetime.now(),
                metadata={'source': 'mock', 'market_type': market_type.value}
            )
            
        except Exception as e:
            logger.error(f"Generic data fetch failed for {symbol}: {e}")
            return None

    def _create_mock_equity_data(self, symbol: str, region: Optional[MarketRegion]) -> GlobalMarketData:
        """Create mock equity data"""
        return GlobalMarketData(
            symbol=symbol,
            market_type=MarketType.EQUITY,
            region=region or MarketRegion.NORTH_AMERICA,
            price=150.0 + np.random.uniform(-20, 20),
            volume=2000000 + np.random.uniform(-500000, 500000),
            change_24h=np.random.uniform(-10, 10),
            change_percent_24h=np.random.uniform(-0.08, 0.08),
            market_cap=50000000000 + np.random.uniform(-**********0, **********0),
            trading_session=TradingSession.NEW_YORK,
            currency='USD',
            timestamp=datetime.now(),
            metadata={'source': 'mock_equity', 'exchange': 'NYSE'}
        )

    def _create_mock_crypto_data(self, symbol: str) -> GlobalMarketData:
        """Create mock cryptocurrency data"""
        base_prices = {'BTC/USDT': 45000, 'ETH/USDT': 3000, 'BNB/USDT': 400}
        base_price = base_prices.get(symbol, 100)
        
        return GlobalMarketData(
            symbol=symbol,
            market_type=MarketType.CRYPTOCURRENCY,
            region=MarketRegion.GLOBAL,
            price=base_price + np.random.uniform(-base_price*0.1, base_price*0.1),
            volume=500000 + np.random.uniform(-100000, 100000),
            change_24h=np.random.uniform(-base_price*0.1, base_price*0.1),
            change_percent_24h=np.random.uniform(-0.15, 0.15),
            market_cap=base_price * 19000000,  # Approximate for BTC
            trading_session=TradingSession.GLOBAL,
            currency='USDT',
            timestamp=datetime.now(),
            metadata={'source': 'mock_crypto', 'exchange': 'binance'}
        )

    def _create_mock_forex_data(self, symbol: str) -> GlobalMarketData:
        """Create mock forex data"""
        base_rates = {'EUR/USD': 1.08, 'GBP/USD': 1.25, 'USD/JPY': 150}
        base_rate = base_rates.get(symbol, 1.0)
        
        return GlobalMarketData(
            symbol=symbol,
            market_type=MarketType.FOREX,
            region=MarketRegion.GLOBAL,
            price=base_rate + np.random.uniform(-base_rate*0.02, base_rate*0.02),
            volume=**********,  # Daily volume
            change_24h=np.random.uniform(-0.02, 0.02),
            change_percent_24h=np.random.uniform(-0.02, 0.02),
            market_cap=None,
            trading_session=TradingSession.LONDON,
            currency='USD',
            timestamp=datetime.now(),
            metadata={'source': 'mock_forex', 'provider': 'interbank'}
        )

    def _create_mock_commodity_data(self, symbol: str) -> GlobalMarketData:
        """Create mock commodity data"""
        base_prices = {'GC=F': 2000, 'CL=F': 80, 'SI=F': 25}  # Gold, Oil, Silver
        base_price = base_prices.get(symbol, 100)
        
        return GlobalMarketData(
            symbol=symbol,
            market_type=MarketType.COMMODITIES,
            region=MarketRegion.GLOBAL,
            price=base_price + np.random.uniform(-base_price*0.05, base_price*0.05),
            volume=50000 + np.random.uniform(-10000, 10000),
            change_24h=np.random.uniform(-base_price*0.03, base_price*0.03),
            change_percent_24h=np.random.uniform(-0.03, 0.03),
            market_cap=None,
            trading_session=TradingSession.NEW_YORK,
            currency='USD',
            timestamp=datetime.now(),
            metadata={'source': 'mock_commodity', 'exchange': 'COMEX'}
        )

    async def _get_current_session(self) -> TradingSession:
        """Get current active trading session"""
        try:
            # Simplified session detection based on UTC time
            current_hour = datetime.utcnow().hour
            
            if 21 <= current_hour or current_hour < 6:
                return TradingSession.SYDNEY
            elif 0 <= current_hour < 9:
                return TradingSession.TOKYO
            elif 8 <= current_hour < 16:
                return TradingSession.LONDON
            elif 14 <= current_hour < 21:
                return TradingSession.NEW_YORK
            else:
                return TradingSession.AFTER_HOURS
                
        except Exception as e:
            logger.error(f"Current session detection failed: {e}")
            return TradingSession.NEW_YORK

    async def _update_active_sessions(self):
        """Update list of currently active trading sessions"""
        try:
            current_session = await self._get_current_session()
            self.active_sessions = [current_session]
            
            # Add overlapping sessions
            if current_session == TradingSession.LONDON:
                self.active_sessions.append(TradingSession.NEW_YORK)
            elif current_session == TradingSession.TOKYO:
                self.active_sessions.append(TradingSession.SYDNEY)
                
        except Exception as e:
            logger.error(f"Active sessions update failed: {e}")

    async def analyze_cross_market_correlations(self, symbols: List[str], 
                                              time_period: str = '30d') -> List[CrossMarketCorrelation]:
        """Analyze correlations across different markets"""
        try:
            correlations = []
            
            # Get market data for all symbols
            all_data = {}
            for symbol in symbols:
                # Determine market type from symbol
                market_type = self._determine_market_type(symbol)
                data = await self.get_global_market_data([symbol], market_type)
                if data:
                    all_data[symbol] = data[0]
            
            # Calculate pairwise correlations
            for i, symbol1 in enumerate(symbols):
                for symbol2 in symbols[i+1:]:
                    if symbol1 in all_data and symbol2 in all_data:
                        # Simulate correlation calculation
                        correlation = np.random.uniform(-0.8, 0.8)
                        significance = np.random.uniform(0.6, 0.99)
                        
                        correlations.append(CrossMarketCorrelation(
                            market_pair=(symbol1, symbol2),
                            correlation_coefficient=correlation,
                            significance=significance,
                            time_period=time_period,
                            market_types=(all_data[symbol1].market_type, all_data[symbol2].market_type),
                            regions=(all_data[symbol1].region, all_data[symbol2].region),
                            analysis_timestamp=datetime.now()
                        ))
            
            return correlations
            
        except Exception as e:
            logger.error(f"Cross-market correlation analysis failed: {e}")
            return []

    def _determine_market_type(self, symbol: str) -> MarketType:
        """Determine market type from symbol"""
        if '/' in symbol and any(crypto in symbol for crypto in ['BTC', 'ETH', 'USDT']):
            return MarketType.CRYPTOCURRENCY
        elif '/' in symbol and len(symbol) == 7:  # EUR/USD format
            return MarketType.FOREX
        elif '=F' in symbol:  # Futures format
            return MarketType.COMMODITIES
        else:
            return MarketType.EQUITY

    async def get_global_market_sentiment(self, region: MarketRegion) -> GlobalMarketSentiment:
        """Get global market sentiment for specific region"""
        try:
            # Check cache
            cache_key = f"sentiment_{region.value}"
            if cache_key in self.sentiment_cache:
                cached = self.sentiment_cache[cache_key]
                if (datetime.now() - cached['timestamp']).seconds < self.cache_ttl * 2:  # Longer cache for sentiment
                    return cached['data']
            
            # Generate sentiment analysis
            sentiment = GlobalMarketSentiment(
                region=region,
                overall_sentiment=np.random.uniform(-0.5, 0.5),
                fear_greed_index=np.random.uniform(0.2, 0.8),
                volatility_index=np.random.uniform(0.1, 0.4),
                risk_on_off=np.random.choice(['risk_on', 'risk_off', 'neutral']),
                dominant_themes=['inflation_concerns', 'geopolitical_tensions', 'tech_earnings'],
                market_drivers=['central_bank_policy', 'economic_data', 'corporate_earnings'],
                timestamp=datetime.now()
            )
            
            # Cache sentiment
            self.sentiment_cache[cache_key] = {
                'data': sentiment,
                'timestamp': datetime.now()
            }
            
            return sentiment
            
        except Exception as e:
            logger.error(f"Global market sentiment analysis failed for {region.value}: {e}")
            return GlobalMarketSentiment(
                region=region,
                overall_sentiment=0.0,
                fear_greed_index=0.5,
                volatility_index=0.2,
                risk_on_off='neutral',
                dominant_themes=[],
                market_drivers=[],
                timestamp=datetime.now()
            )

    async def find_arbitrage_opportunities(self, asset: str, 
                                         markets: List[str]) -> List[ArbitrageOpportunity]:
        """Find arbitrage opportunities across different markets"""
        try:
            opportunities = []
            
            # Get prices from different markets
            market_prices = {}
            for market in markets:
                # Simulate getting price from different market
                base_price = 100.0
                market_prices[market] = base_price + np.random.uniform(-2, 2)
            
            # Find price differences
            market_list = list(market_prices.keys())
            for i, market1 in enumerate(market_list):
                for market2 in market_list[i+1:]:
                    price_diff = abs(market_prices[market1] - market_prices[market2])
                    
                    # Only consider significant differences
                    if price_diff > 1.0:  # $1 difference threshold
                        profit_potential = price_diff * 0.8  # Account for fees
                        
                        opportunity = ArbitrageOpportunity(
                            opportunity_id=f"arb_{int(datetime.now().timestamp())}",
                            asset=asset,
                            market_1={'name': market1, 'price': market_prices[market1]},
                            market_2={'name': market2, 'price': market_prices[market2]},
                            price_difference=price_diff,
                            profit_potential=profit_potential,
                            execution_complexity='medium',
                            time_sensitivity=5,  # 5 minutes
                            risk_factors=['execution_risk', 'liquidity_risk', 'regulatory_risk'],
                            timestamp=datetime.now()
                        )
                        
                        opportunities.append(opportunity)
            
            # Sort by profit potential
            opportunities.sort(key=lambda x: x.profit_potential, reverse=True)
            
            return opportunities[:5]  # Return top 5 opportunities
            
        except Exception as e:
            logger.error(f"Arbitrage opportunity search failed: {e}")
            return []

    async def get_market_hours_status(self) -> Dict[str, Any]:
        """Get current market hours status globally"""
        try:
            await self._update_active_sessions()
            
            status = {
                'current_utc_time': datetime.utcnow().isoformat(),
                'active_sessions': [session.value for session in self.active_sessions],
                'session_details': {}
            }
            
            for session, details in self.trading_sessions.items():
                is_active = session in self.active_sessions
                status['session_details'][session.value] = {
                    'active': is_active,
                    'start_time': details['start'],
                    'end_time': details['end'],
                    'timezone': details['timezone']
                }
            
            return status
            
        except Exception as e:
            logger.error(f"Market hours status failed: {e}")
            return {'error': str(e)}

    def get_engine_status(self) -> Dict[str, Any]:
        """Get global markets engine status"""
        status = {
            'status': self.status.value,
            'libs_available': self.libs_available,
            'supported_regions': [region.value for region in self.supported_regions],
            'supported_markets': [market.value for market in self.supported_markets],
            'cached_market_data': len(self.market_data_cache),
            'cached_correlations': len(self.correlation_cache),
            'cached_sentiment': len(self.sentiment_cache),
            'active_sessions': [session.value for session in self.active_sessions],
            'equity_sources_count': len(self.equity_sources),
            'crypto_exchanges_count': len(self.crypto_exchanges),
            'forex_pairs_count': len(self.forex_providers.get('major_pairs', [])),
            'grok_integration_available': self.grok_integration_available,
            'grok_enhanced_analyses': len(self.enhanced_analyses)
        }

        # Add Grok engine status if available
        if self.grok_engine:
            status['grok_engine_status'] = self.grok_engine.get_engine_status()

        return status

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasGlobalMarketsEngine",
    "GlobalMarketData",
    "CrossMarketCorrelation",
    "GlobalMarketSentiment",
    "ArbitrageOpportunity",
    "MarketType",
    "MarketRegion",
    "TradingSession"
]
